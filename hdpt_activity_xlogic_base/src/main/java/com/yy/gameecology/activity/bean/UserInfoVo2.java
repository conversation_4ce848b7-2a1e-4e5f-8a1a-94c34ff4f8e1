package com.yy.gameecology.activity.bean;

import java.util.Map;

/**
 * desc:
 *
 * @createBy 曾文帜
 * @create 2020-07-30 15:34
 **/
public class UserInfoVo {
    private Long uid;
    private String avatarUrl;
    private String nick;
    private String nobleId;
    private String ecologyNobleId;
    private String yyno;

    private Map<String,String> viewExt;

    public Long getUid() {
        return uid;
    }

    public void setUid(Long uid) {
        this.uid = uid;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public String getNick() {
        return nick;
    }

    public void setNick(String nick) {
        this.nick = nick;
    }

    public String getNobleId() {
        return nobleId;
    }

    public void setNobleId(String nobleId) {
        this.nobleId = nobleId;
    }

    public String getEcologyNobleId() {
        return ecologyNobleId;
    }

    public void setEcologyNobleId(String ecologyNobleId) {
        this.ecologyNobleId = ecologyNobleId;
    }

    public String getYyno() {
        return yyno;
    }

    public void setYyno(String yyno) {
        this.yyno = yyno;
    }

    public Map<String, String> getViewExt() {
        return viewExt;
    }

    public void setViewExt(Map<String, String> viewExt) {
        this.viewExt = viewExt;
    }
}
