package com.yy.gameecology.hdzj.element.component.dao;

import com.yy.gameecology.activity.dao.mysql.GameecologyDao;
import com.yy.gameecology.common.db.mapper.gameecology.cmpt.Cmpt5159CpHourlyWinnerMapper;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5159CpHourlyWinner;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * CP小时第一名玩法组件数据访问层
 * 
 * <AUTHOR> Generated
 * @date 2025-07-23
 */
@Slf4j
@Repository
public class CpHourlyWinnerComponentDao {

    @Autowired
    private Cmpt5159CpHourlyWinnerMapper cpHourlyWinnerMapper;

    @Autowired
    private GameecologyDao gameecologyDao;

    /**
     * 插入CP小时第一名记录（幂等性）
     * 
     * @param record CP小时第一名记录
     * @return 插入行数
     */
    public int insertWinnerRecord(Cmpt5159CpHourlyWinner record) {
        try {
            return cpHourlyWinnerMapper.insertIgnore(record);
        } catch (Exception e) {
            log.error("插入CP小时第一名记录失败, record:{}", record, e);
            return 0;
        }
    }

    /**
     * 检查事件是否已处理（幂等性检查）
     * 
     * @param actId 活动ID
     * @param cmptUseInx 组件使用索引
     * @param seq 事件序列号
     * @return true-已处理，false-未处理
     */
    public boolean isEventProcessed(Long actId, Long cmptUseInx, String seq) {
        try {
            Cmpt5159CpHourlyWinner record = cpHourlyWinnerMapper.selectBySeq(actId, cmptUseInx, seq);
            return record != null;
        } catch (Exception e) {
            log.error("检查事件是否已处理失败, actId:{}, cmptUseInx:{}, seq:{}", actId, cmptUseInx, seq, e);
            return false;
        }
    }

    /**
     * 根据日期查询历史冠军
     * 
     * @param actId 活动ID
     * @param cmptUseInx 组件使用索引
     * @param dateStr 日期字符串，格式：yyyyMMdd
     * @return CP小时第一名记录列表
     */
    public List<Cmpt5159CpHourlyWinner> queryHistoryWinnersByDate(Long actId, Long cmptUseInx, String dateStr) {
        try {
            return cpHourlyWinnerMapper.selectByDate(actId, cmptUseInx, dateStr);
        } catch (Exception e) {
            log.error("根据日期查询历史冠军失败, actId:{}, cmptUseInx:{}, dateStr:{}", actId, cmptUseInx, dateStr, e);
            return List.of();
        }
    }

    /**
     * 根据ID查询记录
     * 
     * @param id 记录ID
     * @return CP小时第一名记录
     */
    public Cmpt5159CpHourlyWinner getById(Long id) {
        try {
            return cpHourlyWinnerMapper.selectById(id);
        } catch (Exception e) {
            log.error("根据ID查询记录失败, id:{}", id, e);
            return null;
        }
    }

    /**
     * 查询最近的冠军记录
     * 
     * @param actId 活动ID
     * @param cmptUseInx 组件使用索引
     * @param limit 限制条数
     * @return CP小时第一名记录列表
     */
    public List<Cmpt5159CpHourlyWinner> queryLatestWinners(Long actId, Long cmptUseInx, Integer limit) {
        try {
            return cpHourlyWinnerMapper.selectLatest(actId, cmptUseInx, limit);
        } catch (Exception e) {
            log.error("查询最近的冠军记录失败, actId:{}, cmptUseInx:{}", actId, cmptUseInx, e);
            return List.of();
        }
    }

    /**
     * 获取奖池余额
     * 
     * @param actId 活动ID
     * @param cmptUseInx 组件使用索引
     * @param poolBalanceKey Redis Key
     * @return 奖池余额，单位：厘
     */
    public Long getPoolBalance(Long actId, Long cmptUseInx, String poolBalanceKey) {
        try {
            String key = String.format("act_%d_cmpt_%d_%s", actId, cmptUseInx, poolBalanceKey);
            String balanceStr = gameecologyDao.getRedisTemplate().opsForValue().get(key);
            return balanceStr != null ? Long.parseLong(balanceStr) : 0L;
        } catch (Exception e) {
            log.error("获取奖池余额失败, actId:{}, cmptUseInx:{}, key:{}", actId, cmptUseInx, poolBalanceKey, e);
            return 0L;
        }
    }

    /**
     * 扣减奖池余额
     * 
     * @param actId 活动ID
     * @param cmptUseInx 组件使用索引
     * @param poolBalanceKey Redis Key
     * @param amount 扣减金额，单位：厘
     * @return 扣减后的余额
     */
    public Long deductPoolBalance(Long actId, Long cmptUseInx, String poolBalanceKey, Long amount) {
        try {
            String key = String.format("act_%d_cmpt_%d_%s", actId, cmptUseInx, poolBalanceKey);
            return gameecologyDao.getRedisTemplate().opsForValue().decrement(key, amount);
        } catch (Exception e) {
            log.error("扣减奖池余额失败, actId:{}, cmptUseInx:{}, key:{}, amount:{}", 
                     actId, cmptUseInx, poolBalanceKey, amount, e);
            return 0L;
        }
    }

    /**
     * 设置奖池余额
     * 
     * @param actId 活动ID
     * @param cmptUseInx 组件使用索引
     * @param poolBalanceKey Redis Key
     * @param balance 余额，单位：厘
     */
    public void setPoolBalance(Long actId, Long cmptUseInx, String poolBalanceKey, Long balance) {
        try {
            String key = String.format("act_%d_cmpt_%d_%s", actId, cmptUseInx, poolBalanceKey);
            gameecologyDao.getRedisTemplate().opsForValue().set(key, balance.toString());
        } catch (Exception e) {
            log.error("设置奖池余额失败, actId:{}, cmptUseInx:{}, key:{}, balance:{}", 
                     actId, cmptUseInx, poolBalanceKey, balance, e);
        }
    }
}
