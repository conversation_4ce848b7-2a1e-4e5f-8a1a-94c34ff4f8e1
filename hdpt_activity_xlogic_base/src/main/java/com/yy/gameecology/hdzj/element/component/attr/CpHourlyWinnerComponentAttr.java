package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.BizSource;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * CP小时第一名玩法组件属性配置
 * 
 * <AUTHOR> Generated
 * @date 2025-07-23
 */
@Data
public class CpHourlyWinnerComponentAttr extends ComponentAttr {
    
    @ComponentAttrField(labelText = "CP榜单ID")
    private Long cpRankId;
    
    @ComponentAttrField(labelText = "CP阶段ID")
    private Long cpPhaseId;
    
    @ComponentAttrField(labelText = "发奖业务ID", dropDownSourceBeanClass = BizSource.class)
    private Long awardBusiId;
    
    @ComponentAttrField(labelText = "分数区间奖励配置", remark = "根据CP分数区间配置不同奖励",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "分数下限"),
                    @SubField(fieldName = Constant.KEY2, type = Long.class, labelText = "分数上限"),
                    @SubField(fieldName = Constant.VALUE, type = AwardAttrConfig.class, labelText = "奖励配置")
            })
    private Map<Long, Map<Long, AwardAttrConfig>> scoreRangeAwards = Maps.newTreeMap();
    
    @ComponentAttrField(labelText = "全服礼物奖池总限额", remark = "单位：厘，默认4000000厘(40000元)")
    private Long giftPoolTotalLimit = 4000000L;
    
    @ComponentAttrField(labelText = "奖池不足时发放的礼物配置")
    private AwardAttrConfig poolInsufficientAward;
    
    @ComponentAttrField(labelText = "应援口令抽奖组件索引")
    private Long watchwordLotteryComponentIndex;
    
    @ComponentAttrField(labelText = "应援口令过期时间", remark = "单位：秒，默认3600秒(1小时)")
    private Long watchwordExpireSeconds = 3600L;
    
    @ComponentAttrField(labelText = "最新CP组件索引", remark = "用于获取CP所在房间信息")
    private Long latestCpComponentIndex;
    
    @ComponentAttrField(labelText = "广播横幅ID")
    private Long broadcastBannerId;
    
    @ComponentAttrField(labelText = "广播模板类型", remark = "2==宝贝 3==交友 5==语音房(技能卡)")
    private Integer broadcastTemplateType = 810;
    
    @ComponentAttrField(labelText = "广播类型", remark = "4=单业务广播；5=多业务广播")
    private Integer broadcastType = 5;
    
    @ComponentAttrField(labelText = "是否启用广播", remark = "1-启用，0-禁用")
    private Integer enableBroadcast = 1;
    
    @ComponentAttrField(labelText = "是否启用应援口令", remark = "1-启用，0-禁用")
    private Integer enableWatchword = 1;
    
    @ComponentAttrField(labelText = "奖池限额控制key", remark = "用于控制全服礼物奖池限额")
    private String giftPoolLimitKey = "gift_pool_limit";
    
    /**
     * 检查是否为当前组件负责的榜单和阶段
     */
    public boolean isMyDuty(Long rankId, Long phaseId) {
        return this.cpRankId.equals(rankId) && this.cpPhaseId.equals(phaseId);
    }
    
    /**
     * 根据CP分数获取对应的奖励配置
     */
    public AwardAttrConfig getAwardByScore(Long cpScore) {
        if (scoreRangeAwards == null || scoreRangeAwards.isEmpty()) {
            return null;
        }
        
        for (Map.Entry<Long, Map<Long, AwardAttrConfig>> entry : scoreRangeAwards.entrySet()) {
            Long minScore = entry.getKey();
            Map<Long, AwardAttrConfig> maxScoreMap = entry.getValue();
            
            if (cpScore >= minScore) {
                for (Map.Entry<Long, AwardAttrConfig> maxEntry : maxScoreMap.entrySet()) {
                    Long maxScore = maxEntry.getKey();
                    AwardAttrConfig award = maxEntry.getValue();
                    
                    if (maxScore == 0 || cpScore <= maxScore) {
                        return award;
                    }
                }
            }
        }
        
        return null;
    }
}
