package com.yy.gameecology.hdzj.element.component.attr;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yy.gameecology.hdzj.element.ComponentAttr;
import com.yy.gameecology.hdzj.element.attrconfig.BizSource;
import com.yy.gameecology.hdzj.element.attrconfig.ComponentAttrField;
import com.yy.gameecology.hdzj.element.attrconfig.Constant;
import com.yy.gameecology.hdzj.element.attrconfig.SubField;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * CP小时第一名玩法组件属性配置
 * 
 * <AUTHOR> Generated
 * @date 2025-07-23
 */
@Data
public class CpHourlyWinnerComponentAttr extends ComponentAttr {

    @ComponentAttrField(labelText = "CP榜单ID")
    private Long cpRankId;

    @ComponentAttrField(labelText = "CP阶段ID")
    private Long cpPhaseId;

    @ComponentAttrField(labelText = "发奖业务ID")
    private Long awardBusiId;

    @ComponentAttrField(labelText = "分数区间奖励配置", remark = "key为分数下限，value为奖励配置",
            subFields = {
                    @SubField(fieldName = Constant.KEY1, type = Long.class, labelText = "分数下限"),
                    @SubField(fieldName = Constant.VALUE, type = AwardAttrConfig.class, labelText = "奖励配置")
            })
    private Map<Long, AwardAttrConfig> scoreRangeAwards = Maps.newLinkedHashMap();

    @ComponentAttrField(labelText = "全服礼物奖池总限量", remark = "单位：厘")
    private Long totalGiftPoolLimit;

    @ComponentAttrField(labelText = "奖池不足时的替代奖励")
    private AwardAttrConfig alternativeAward;

    @ComponentAttrField(labelText = "应援口令文本")
    private String watchwordText = "夏日派对，浪漫加倍";

    @ComponentAttrField(labelText = "应援口令过期时间", remark = "单位：秒")
    private Long watchwordExpireSeconds = 3600L;

    @ComponentAttrField(labelText = "ChannelWatchwordLotteryComponent组件索引")
    private Long watchwordLotteryComponentIndex;

    @ComponentAttrField(labelText = "LatestCpComponent组件索引")
    private Long latestCpComponentIndex;

    @ComponentAttrField(labelText = "广播横幅ID")
    private Long broadcastBannerId;

    @ComponentAttrField(labelText = "广播横幅类型")
    private Long broadcastBannerType;

    @ComponentAttrField(labelText = "广播类型", remark = "2-子频道广播 3-顶级频道广播 4-全模板广播")
    private Long broadcastType = 4L;

    @ComponentAttrField(labelText = "奖池余额Redis Key")
    private String poolBalanceRedisKey = "cp_hourly_winner_pool_balance";

    @ComponentAttrField(labelText = "是否启用广播", remark = "true-启用，false-禁用")
    private Boolean enableBroadcast = true;

    @ComponentAttrField(labelText = "是否启用应援口令", remark = "true-启用，false-禁用")
    private Boolean enableWatchword = true;

    @ComponentAttrField(labelText = "奖励总限额", remark = "单位：厘，0表示不限额")
    private Long totalAwardLimit = 0L;

    @ComponentAttrField(labelText = "每日奖励限额", remark = "单位：厘，0表示不限额")
    private Long dailyAwardLimit = 0L;

    @ComponentAttrField(labelText = "每小时奖励限额", remark = "单位：厘，0表示不限额")
    private Long hourlyAwardLimit = 0L;

    /**
     * 根据分数获取对应的奖励配置
     * 
     * @param score CP分数
     * @return 奖励配置，如果没有匹配的区间则返回null
     */
    public AwardAttrConfig getAwardByScore(Long score) {
        if (score == null || scoreRangeAwards.isEmpty()) {
            return null;
        }
        
        AwardAttrConfig matchedAward = null;
        Long maxMatchedScore = null;
        
        for (Map.Entry<Long, AwardAttrConfig> entry : scoreRangeAwards.entrySet()) {
            Long minScore = entry.getKey();
            if (score >= minScore) {
                if (maxMatchedScore == null || minScore > maxMatchedScore) {
                    maxMatchedScore = minScore;
                    matchedAward = entry.getValue();
                }
            }
        }
        
        return matchedAward;
    }
}
