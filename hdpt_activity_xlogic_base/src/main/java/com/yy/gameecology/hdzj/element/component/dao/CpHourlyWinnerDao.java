package com.yy.gameecology.hdzj.element.component.dao;

import com.yy.gameecology.common.db.mapper.gameecology.cmpt.Cmpt5159CpHourlyWinnerMapper;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5159CpHourlyWinner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * CP小时第一名玩法组件数据访问层
 * 
 * <AUTHOR> Generated
 * @date 2025-07-23
 */
@Repository
public class CpHourlyWinnerDao {
    
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    
    @Autowired
    private Cmpt5159CpHourlyWinnerMapper cmpt5159CpHourlyWinnerMapper;
    
    /**
     * 插入CP小时第一名记录（幂等性插入）
     */
    public boolean insertHourlyWinner(Cmpt5159CpHourlyWinner record) {
        try {
            int result = cmpt5159CpHourlyWinnerMapper.insertIgnore(record);
            log.info("Insert hourly winner result:{}, record:{}", result, record);
            return result > 0;
        } catch (Exception e) {
            log.error("Insert hourly winner failed, record:{}", record, e);
            return false;
        }
    }
    
    /**
     * 检查事件是否已处理（幂等性检查）
     */
    public boolean isEventProcessed(Long actId, Long cmptUseInx, String seq) {
        try {
            int count = cmpt5159CpHourlyWinnerMapper.countBySeq(actId, cmptUseInx, seq);
            return count > 0;
        } catch (Exception e) {
            log.error("Check event processed failed, actId:{}, cmptUseInx:{}, seq:{}", actId, cmptUseInx, seq, e);
            return false;
        }
    }
    
    /**
     * 根据日期查询历史小时冠军CP
     */
    public List<Cmpt5159CpHourlyWinner> queryHistoryWinnersByDate(Long actId, Long cmptUseInx, String datePrefix) {
        try {
            return cmpt5159CpHourlyWinnerMapper.selectByDate(actId, cmptUseInx, datePrefix);
        } catch (Exception e) {
            log.error("Query history winners by date failed, actId:{}, cmptUseInx:{}, datePrefix:{}", 
                    actId, cmptUseInx, datePrefix, e);
            return null;
        }
    }
    
    /**
     * 根据小时编码查询记录
     */
    public Cmpt5159CpHourlyWinner queryByHourCode(Long actId, Long cmptUseInx, String hourCode) {
        try {
            return cmpt5159CpHourlyWinnerMapper.selectByHourCode(actId, cmptUseInx, hourCode);
        } catch (Exception e) {
            log.error("Query by hour code failed, actId:{}, cmptUseInx:{}, hourCode:{}", 
                    actId, cmptUseInx, hourCode, e);
            return null;
        }
    }
    
    /**
     * 查询最近的冠军记录
     */
    public List<Cmpt5159CpHourlyWinner> queryLatestWinners(Long actId, Long cmptUseInx, Integer limit) {
        try {
            return cmpt5159CpHourlyWinnerMapper.selectLatest(actId, cmptUseInx, limit);
        } catch (Exception e) {
            log.error("Query latest winners failed, actId:{}, cmptUseInx:{}, limit:{}", 
                    actId, cmptUseInx, limit, e);
            return null;
        }
    }
    
    /**
     * 根据榜单ID和阶段ID查询记录
     */
    public List<Cmpt5159CpHourlyWinner> queryByRankAndPhase(Long actId, Long cmptUseInx, Long rankId, Long phaseId, Integer limit) {
        try {
            return cmpt5159CpHourlyWinnerMapper.selectByRankAndPhase(actId, cmptUseInx, rankId, phaseId, limit);
        } catch (Exception e) {
            log.error("Query by rank and phase failed, actId:{}, cmptUseInx:{}, rankId:{}, phaseId:{}, limit:{}", 
                    actId, cmptUseInx, rankId, phaseId, limit, e);
            return null;
        }
    }
    
    /**
     * 根据ID查询记录
     */
    public Cmpt5159CpHourlyWinner queryById(Long id) {
        try {
            return cmpt5159CpHourlyWinnerMapper.selectById(id);
        } catch (Exception e) {
            log.error("Query by id failed, id:{}", id, e);
            return null;
        }
    }
}
