package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.bean.hdzt.RankingTimeEnd;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.activity.service.UserInfoService;
import com.yy.gameecology.common.bean.MultiNickItem;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt2061LatestCp;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5159CpHourlyWinner;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.CpHourlyWinnerComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import com.yy.gameecology.hdzj.element.component.dao.CpHourlyWinnerDao;
import com.yy.thrift.hdztranking.Rank;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * CP小时第一名玩法组件
 * 
 * <AUTHOR> Generated
 * @date 2025-07-23
 */
@Component
@RestController
@RequestMapping("/5159")
public class CpHourlyWinnerComponent extends BaseActComponent<CpHourlyWinnerComponentAttr> {
    
    private final Logger log = LoggerFactory.getLogger(this.getClass());
    
    @Autowired
    private CpHourlyWinnerDao cpHourlyWinnerDao;
    
    @Autowired
    private CommonDataDao commonDataDao;
    
    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;
    
    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;
    
    @Autowired
    private UserInfoService userInfoService;
    
    @Autowired
    private CommonService commonService;
    
    @Autowired
    private CommonBroadCastService commonBroadCastService;
    
    @Autowired
    private ChannelWatchwordLotteryComponent channelWatchwordLotteryComponent;
    
    @Autowired
    private LatestCpComponent latestCpComponent;
    
    @Override
    public Long getComponentId() {
        return ComponentId.CP_HOURLY_WINNER;
    }
    
    /**
     * 榜单结束事件处理方法
     * 每小时结束时，记录CP榜单第一名并进行相关处理
     */
    @HdzjEventHandler(value = RankingTimeEnd.class, canRetry = true)
    public void onRankingTimeEnd(RankingTimeEnd event, CpHourlyWinnerComponentAttr attr) {
        log.info("onRankingTimeEnd start, event:{}, attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));
        
        // 检查是否为当前组件负责的榜单和阶段
        if (!attr.isMyDuty(event.getRankId(), event.getPhaseId())) {
            log.info("Not my duty, rankId:{}, phaseId:{}", event.getRankId(), event.getPhaseId());
            return;
        }
        
        // 获取事件序列号，确保幂等性
        String seq = getEventSeq(event);
        if (cpHourlyWinnerDao.isEventProcessed(attr.getActId(), attr.getCmptUseInx(), seq)) {
            log.warn("Event already processed, seq:{}", seq);
            return;
        }
        
        try {
            // 处理榜单结束事件
            processRankingEnd(event, attr, seq);
        } catch (Exception e) {
            log.error("Process ranking end failed, event:{}, attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr), e);
            throw e;
        }
    }
    
    /**
     * 处理榜单结束事件的主逻辑
     */
    private void processRankingEnd(RankingTimeEnd event, CpHourlyWinnerComponentAttr attr, String seq) {
        // 获取小时编码
        Date endTime = DateUtil.parseDate(event.getEndTime(), DateUtil.PATTERN_TYPE1);
        String hourCode = DateUtil.format(endTime, "yyyyMMddHH");
        
        // 查询榜单第一名
        List<Rank> ranks = hdztRankingThriftClient.queryRanking(
                attr.getActId(), 
                event.getRankId(), 
                event.getPhaseId(), 
                TimeKeyHelper.getTimeKey(endTime, event.getTimeKey()), 
                1, 
                new HashMap<>()
        );
        
        if (ranks == null || ranks.isEmpty()) {
            log.info("No ranking data found for hourCode:{}", hourCode);
            return;
        }
        
        Rank firstRank = ranks.get(0);
        String[] cpMembers = Const.splitCpMember(firstRank.getMember());
        if (cpMembers.length != 2) {
            log.warn("Invalid cp member format:{}", firstRank.getMember());
            return;
        }
        
        Long userUid = Long.valueOf(cpMembers[0]);
        Long anchorUid = Long.valueOf(cpMembers[1]);
        Long cpScore = firstRank.getScore();
        
        log.info("Found first rank CP: userUid:{}, anchorUid:{}, score:{}", userUid, anchorUid, cpScore);
        
        // TODO: 实现以下功能
        // 1. 记录CP榜单第一名
        recordHourlyWinner(attr, seq, event.getRankId(), event.getPhaseId(), hourCode, userUid, anchorUid, cpScore);
        
        // 2. 发放奖励
        awardToWinner(userUid, anchorUid, cpScore, attr, seq);
        
        // 3. 发送应援口令到房间
        sendWatchwordToRoom(userUid, anchorUid, attr);
        
        // 4. 全服广播第一名CP信息
        broadcastWinnerInfo(userUid, anchorUid, attr);
    }
    
    /**
     * 记录CP小时第一名
     */
    private void recordHourlyWinner(CpHourlyWinnerComponentAttr attr, String seq, Long rankId, Long phaseId, 
                                   String hourCode, Long userUid, Long anchorUid, Long cpScore) {
        // TODO: 实现记录逻辑
        log.info("TODO: Record hourly winner - seq:{}, hourCode:{}, userUid:{}, anchorUid:{}, score:{}", 
                seq, hourCode, userUid, anchorUid, cpScore);
    }
    
    /**
     * 对第一名CP发放奖励
     */
    private void awardToWinner(Long userUid, Long anchorUid, Long cpScore, CpHourlyWinnerComponentAttr attr, String seq) {
        // TODO: 实现发奖逻辑
        log.info("TODO: Award to winner - userUid:{}, anchorUid:{}, score:{}", userUid, anchorUid, cpScore);
    }
    
    /**
     * 发送应援口令到房间
     */
    private void sendWatchwordToRoom(Long userUid, Long anchorUid, CpHourlyWinnerComponentAttr attr) {
        // TODO: 调用ChannelWatchwordLotteryComponent的具体方法
        log.info("TODO: Send watchword to room - userUid:{}, anchorUid:{}", userUid, anchorUid);
    }
    
    /**
     * 全服广播第一名CP信息
     */
    private void broadcastWinnerInfo(Long userUid, Long anchorUid, CpHourlyWinnerComponentAttr attr) {
        // TODO: 实现广播逻辑
        log.info("TODO: Broadcast winner info - userUid:{}, anchorUid:{}", userUid, anchorUid);
    }
    
    /**
     * HTTP接口1: 查询历史小时冠军CP
     */
    @RequestMapping("/queryHistoryWinners")
    public Response<List<HistoryWinnerVo>> queryHistoryWinners(
            @RequestParam Long actId,
            @RequestParam Long cmptUseInx,
            @RequestParam String date) {
        
        try {
            // TODO: 实现查询历史冠军CP的逻辑
            log.info("TODO: Query history winners - actId:{}, cmptUseInx:{}, date:{}", actId, cmptUseInx, date);
            return Response.success(null);
        } catch (Exception e) {
            log.error("Query history winners failed", e);
            return Response.fail(400, "查询失败");
        }
    }
    
    /**
     * HTTP接口2: 查询全服礼物奖池余额
     */
    @RequestMapping("/queryGiftPoolBalance")
    public Response<GiftPoolBalanceVo> queryGiftPoolBalance(
            @RequestParam Long actId,
            @RequestParam Long cmptUseInx) {
        
        try {
            // TODO: 实现查询奖池余额的逻辑
            log.info("TODO: Query gift pool balance - actId:{}, cmptUseInx:{}", actId, cmptUseInx);
            return Response.success(null);
        } catch (Exception e) {
            log.error("Query gift pool balance failed", e);
            return Response.fail(400, "查询失败");
        }
    }
    
    /**
     * 历史冠军VO
     */
    @Data
    public static class HistoryWinnerVo {
        private String userNickname;
        private String userAvatar;
        private String anchorNickname;
        private String anchorAvatar;
        private String hourCode;
        private Long cpScore;
        private Map<String, Map<String, MultiNickItem>> nickExtUsers;
    }
    
    /**
     * 奖池余额VO
     */
    @Data
    public static class GiftPoolBalanceVo {
        private Long totalLimit;
        private Long usedAmount;
        private Long remainingAmount;
    }
}
