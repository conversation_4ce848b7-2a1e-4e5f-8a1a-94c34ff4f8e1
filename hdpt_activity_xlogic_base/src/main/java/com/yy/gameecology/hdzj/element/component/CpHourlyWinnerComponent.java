package com.yy.gameecology.hdzj.element.component;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Maps;
import com.yy.gameecology.activity.bean.Response;
import com.yy.gameecology.activity.bean.UserInfoVo;
import com.yy.gameecology.activity.bean.hdzt.RankingTimeEnd;
import com.yy.gameecology.activity.client.thrift.HdztAwardServiceClient;
import com.yy.gameecology.activity.client.thrift.HdztRankingThriftClient;
import com.yy.gameecology.activity.commons.TimeKeyHelper;
import com.yy.gameecology.activity.dao.mysql.CommonDataDao;
import com.yy.gameecology.activity.service.CommonBroadCastService;
import com.yy.gameecology.activity.service.CommonService;
import com.yy.gameecology.activity.service.UserInfoService;
import com.yy.gameecology.common.bean.MultiNickItem;
import com.yy.gameecology.common.bean.Template;
import com.yy.gameecology.common.consts.Const;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt2061LatestCp;
import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5159CpHourlyWinner;
import com.yy.gameecology.common.utils.DateUtil;
import com.yy.gameecology.common.utils.RankUtils;
import com.yy.thrift.hdztaward.BatchWelfareResult;
import com.yy.gameecology.hdzj.BaseActComponent;
import com.yy.gameecology.hdzj.annotation.HdzjEventHandler;
import com.yy.gameecology.hdzj.consts.ComponentId;
import com.yy.gameecology.hdzj.element.component.attr.CpHourlyWinnerComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import com.yy.gameecology.hdzj.element.component.dao.CpHourlyWinnerComponentDao;
import com.yy.thrift.hdztranking.Rank;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * CP小时第一名玩法组件
 * 组件ID: 5159
 * 
 * 功能：
 * 1. 每小时结束时，记录CP榜单第一名
 * 2. 对第一名CP发放奖励，根据分数区间不同发放不同奖励
 * 3. 管理全服礼物奖池，奖池不足时发放替代奖励
 * 4. 对第一名CP所在房间发送应援口令抽奖
 * 5. 全服广播第一名CP信息
 * 6. 提供HTTP接口查询历史冠军和奖池余额
 * 
 * <AUTHOR> Generated
 * @date 2025-07-23
 */
@Slf4j
@Component
@RestController
@RequestMapping("/5159")
public class CpHourlyWinnerComponent extends BaseActComponent<CpHourlyWinnerComponentAttr> {

    @Autowired
    private CpHourlyWinnerComponentDao cpHourlyWinnerComponentDao;

    @Autowired
    private CommonService commonService;

    @Autowired
    private UserInfoService userInfoService;

    @Autowired
    private CommonBroadCastService commonBroadCastService;

    @Autowired
    private ChannelWatchwordLotteryComponent channelWatchwordLotteryComponent;

    @Autowired
    private LatestCpComponent latestCpComponent;

    @Autowired
    private HdztRankingThriftClient hdztRankingThriftClient;

    @Autowired
    private HdztAwardServiceClient hdztAwardServiceClient;

    @Autowired
    private CommonDataDao commonDataDao;

    @Override
    public Long getComponentId() {
        return ComponentId.CP_HOURLY_WINNER;
    }

    /**
     * 监听榜单结束事件，处理每小时CP榜单第一名结算
     */
    @HdzjEventHandler(value = RankingTimeEnd.class, canRetry = true)
    public void onRankingTimeEnd(RankingTimeEnd event, CpHourlyWinnerComponentAttr attr) {
        log.info("onRankingTimeEnd start event:{}, attr:{}", JSON.toJSONString(event), JSON.toJSONString(attr));
        
        // 检查是否是我们关心的榜单
        if (event.getRankId() != attr.getCpRankId()) {
            return;
        }
        
        // 防重检查
        String eventSeq = event.getSeq();
        if (cpHourlyWinnerComponentDao.isEventProcessed(attr.getActId(), attr.getCmptUseInx(), eventSeq)) {
            log.warn("Event already processed, skip. eventSeq:{}", eventSeq);
            return;
        }
        
        try {
            // 处理小时榜单结算
            processHourlyRankSettle(event, attr);
        } catch (Exception e) {
            log.error("处理小时榜单结算失败, event:{}, attr:{}", event, attr, e);
            throw e; // 重新抛出异常，触发重试机制
        }
    }

    /**
     * 处理小时榜单结算
     * 实现榜单结算的主逻辑
     */
    private void processHourlyRankSettle(RankingTimeEnd event, CpHourlyWinnerComponentAttr attr) {
        log.info("processHourlyRankSettle start, event:{}", event);

        // 1. 查询榜单第一名
        Rank firstRank = queryFirstRankWinner(event, attr);
        if (firstRank == null) {
            log.warn("No first rank winner found for event:{}", event);
            return;
        }

        // 解析CP成员
        Pair<Long, Long> cpUids = RankUtils.getCpUidByMemberId(firstRank.getMember());
        if (cpUids == null) {
            log.error("Failed to parse CP member from:{}", firstRank.getMember());
            return;
        }

        Long userUid = cpUids.getLeft();
        Long anchorUid = cpUids.getRight();
        Long cpScore = firstRank.getScore();

        log.info("Found first rank winner: userUid:{}, anchorUid:{}, score:{}", userUid, anchorUid, cpScore);

        // 2. 记录第一名信息到数据库
        Cmpt5159CpHourlyWinner winnerRecord = createWinnerRecord(event, attr, userUid, anchorUid, cpScore);
        int insertResult = cpHourlyWinnerComponentDao.insertWinnerRecord(winnerRecord);
        if (insertResult <= 0) {
            log.warn("Failed to insert winner record, may be duplicate. seq:{}", event.getSeq());
            return;
        }

        // 3. 发放奖励
        processAwardDistribution(userUid, anchorUid, cpScore, attr, winnerRecord);

        // 4. 发送应援口令到房间
        if (attr.getEnableWatchword()) {
            sendWatchwordToRoom(userUid, anchorUid, attr);
        }

        // 5. 全服广播第一名CP信息
        if (attr.getEnableBroadcast()) {
            broadcastWinnerInfo(userUid, anchorUid, cpScore, attr);
        }

        log.info("processHourlyRankSettle completed successfully");
    }

    /**
     * 查询榜单第一名
     */
    private Rank queryFirstRankWinner(RankingTimeEnd event, CpHourlyWinnerComponentAttr attr) {
        try {
            // 获取时间编码
            String timeCode = TimeKeyHelper.getTimeCode(event.getTimeKey(), DateUtil.getDate(event.getEndTime()));

            // 查询榜单第一名
            List<Rank> ranks = hdztRankingThriftClient.queryRanking(
                event.getActId(),
                event.getRankId(),
                event.getPhaseId(),
                timeCode,
                1, // 只查询第一名
                Maps.newHashMap()
            );

            if (CollectionUtils.isEmpty(ranks)) {
                log.warn("No ranks found for actId:{}, rankId:{}, phaseId:{}, timeCode:{}",
                        event.getActId(), event.getRankId(), event.getPhaseId(), timeCode);
                return null;
            }

            return ranks.get(0);
        } catch (Exception e) {
            log.error("Failed to query first rank winner", e);
            return null;
        }
    }

    /**
     * 创建获胜者记录
     */
    private Cmpt5159CpHourlyWinner createWinnerRecord(RankingTimeEnd event, CpHourlyWinnerComponentAttr attr,
                                                      Long userUid, Long anchorUid, Long cpScore) {
        Cmpt5159CpHourlyWinner record = new Cmpt5159CpHourlyWinner();
        record.setActId(attr.getActId());
        record.setCmptUseInx(attr.getCmptUseInx());
        record.setSeq(event.getSeq());
        record.setRankId(event.getRankId());
        record.setPhaseId(event.getPhaseId());

        // 生成小时编码
        String hourCode = TimeKeyHelper.getTimeCode(event.getTimeKey(), DateUtil.getDate(event.getEndTime()));
        record.setHourCode(hourCode);

        record.setUserUid(userUid);
        record.setAnchorUid(anchorUid);
        record.setCpScore(cpScore);

        // 初始化奖励相关字段，后续在奖励发放时更新
        record.setAwardAmount(0L);
        record.setIsPoolSufficient(1);

        return record;
    }

    /**
     * 处理奖励发放
     */
    private void processAwardDistribution(Long userUid, Long anchorUid, Long cpScore,
                                        CpHourlyWinnerComponentAttr attr, Cmpt5159CpHourlyWinner winnerRecord) {
        try {
            // 根据分数获取奖励配置
            AwardAttrConfig awardConfig = attr.getAwardByScore(cpScore);
            if (awardConfig == null) {
                log.warn("No award config found for score:{}", cpScore);
                return;
            }

            // 检查奖池余额
            Long currentBalance = cpHourlyWinnerComponentDao.getPoolBalance(
                attr.getActId(), attr.getCmptUseInx(), attr.getPoolBalanceRedisKey());

            boolean isPoolSufficient = currentBalance >= awardConfig.getAwardAmount();
            AwardAttrConfig finalAwardConfig = isPoolSufficient ? awardConfig : attr.getAlternativeAward();

            if (finalAwardConfig == null) {
                log.warn("No final award config available, poolSufficient:{}", isPoolSufficient);
                return;
            }

            // 检查限额并发放奖励
            boolean awardSuccess = checkLimitAndDistributeAward(userUid, anchorUid, finalAwardConfig, attr, winnerRecord);

            // 更新奖池余额（如果使用了礼物奖池且发放成功）
            if (awardSuccess && isPoolSufficient && awardConfig.getAwardAmount() > 0) {
                cpHourlyWinnerComponentDao.deductPoolBalance(
                    attr.getActId(), attr.getCmptUseInx(), attr.getPoolBalanceRedisKey(), awardConfig.getAwardAmount());
            }

            // 更新记录中的奖励信息
            winnerRecord.setAwardAmount(awardSuccess ? finalAwardConfig.getAwardAmount() : 0L);
            winnerRecord.setIsPoolSufficient(isPoolSufficient ? 1 : 0);

            log.info("Award distribution completed for userUid:{}, anchorUid:{}, isPoolSufficient:{}, awardSuccess:{}",
                    userUid, anchorUid, isPoolSufficient, awardSuccess);

        } catch (Exception e) {
            log.error("Failed to process award distribution for userUid:{}, anchorUid:{}", userUid, anchorUid, e);
        }
    }

    /**
     * 检查限额并发放奖励
     */
    private boolean checkLimitAndDistributeAward(Long userUid, Long anchorUid, AwardAttrConfig awardConfig,
                                               CpHourlyWinnerComponentAttr attr, Cmpt5159CpHourlyWinner winnerRecord) {
        try {
            Long awardAmount = awardConfig.getAwardAmount();
            String baseSeq = winnerRecord.getSeq();

            // 检查总限额
            if (attr.getTotalAwardLimit() > 0) {
                CommonDataDao.ValueIncResult totalResult = commonDataDao.valueIncrIgnoreWithLimit(
                    attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(),
                    baseSeq + "_total", "total_award_limit", awardAmount, attr.getTotalAwardLimit());

                if (totalResult.isViolateLimit()) {
                    log.warn("Total award limit exceeded, userUid:{}, anchorUid:{}, awardAmount:{}",
                            userUid, anchorUid, awardAmount);
                    return false;
                }
            }

            // 检查每日限额
            if (attr.getDailyAwardLimit() > 0) {
                String dayCode = DateUtil.format(commonService.getNow(attr.getActId()), DateUtil.PATTERN_TYPE2);
                CommonDataDao.ValueIncResult dailyResult = commonDataDao.valueIncrIgnoreWithLimit(
                    attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(),
                    baseSeq + "_daily", "daily_award_limit_" + dayCode, awardAmount, attr.getDailyAwardLimit());

                if (dailyResult.isViolateLimit()) {
                    log.warn("Daily award limit exceeded, userUid:{}, anchorUid:{}, awardAmount:{}, dayCode:{}",
                            userUid, anchorUid, awardAmount, dayCode);
                    return false;
                }
            }

            // 检查每小时限额
            if (attr.getHourlyAwardLimit() > 0) {
                String hourCode = winnerRecord.getHourCode();
                CommonDataDao.ValueIncResult hourlyResult = commonDataDao.valueIncrIgnoreWithLimit(
                    attr.getActId(), attr.getCmptId(), attr.getCmptUseInx(),
                    baseSeq + "_hourly", "hourly_award_limit_" + hourCode, awardAmount, attr.getHourlyAwardLimit());

                if (hourlyResult.isViolateLimit()) {
                    log.warn("Hourly award limit exceeded, userUid:{}, anchorUid:{}, awardAmount:{}, hourCode:{}",
                            userUid, anchorUid, awardAmount, hourCode);
                    return false;
                }
            }

            // 所有限额检查通过，发放奖励
            distributeAwardToUser(userUid, awardConfig, attr, baseSeq + "_user");
            distributeAwardToAnchor(anchorUid, awardConfig, attr, baseSeq + "_anchor");

            return true;

        } catch (Exception e) {
            log.error("Failed to check limit and distribute award, userUid:{}, anchorUid:{}", userUid, anchorUid, e);
            return false;
        }
    }

    /**
     * 发放奖励给用户
     */
    private void distributeAwardToUser(Long userUid, AwardAttrConfig awardConfig,
                                     CpHourlyWinnerComponentAttr attr, String seq) {
        try {
            if (awardConfig.getTAwardTskId() == null || awardConfig.getTAwardTskId() <= 0) {
                log.warn("Invalid award task id for user:{}", userUid);
                return;
            }

            String time = DateUtil.format(commonService.getNow(attr.getActId()));

            if (awardConfig.getTAwardPkgId() != null && awardConfig.getTAwardPkgId() > 0) {
                // 发放指定奖品包
                BatchWelfareResult result = hdztAwardServiceClient.doWelfare(
                    time,
                    attr.getAwardBusiId(),
                    userUid,
                    awardConfig.getTAwardTskId(),
                    awardConfig.getNum(),
                    awardConfig.getTAwardPkgId(),
                    seq,
                    Maps.newHashMap()
                );

                log.info("Distribute award to user result: uid:{}, result:{}", userUid, result);
            } else {
                // 抽奖模式
                log.info("Award config is lottery mode for user:{}, taskId:{}", userUid, awardConfig.getTAwardTskId());
                // TODO: 实现抽奖逻辑
            }

        } catch (Exception e) {
            log.error("Failed to distribute award to user:{}", userUid, e);
        }
    }

    /**
     * 发放奖励给主播
     */
    private void distributeAwardToAnchor(Long anchorUid, AwardAttrConfig awardConfig,
                                       CpHourlyWinnerComponentAttr attr, String seq) {
        try {
            if (awardConfig.getTAwardTskId() == null || awardConfig.getTAwardTskId() <= 0) {
                log.warn("Invalid award task id for anchor:{}", anchorUid);
                return;
            }

            String time = DateUtil.format(commonService.getNow(attr.getActId()));

            if (awardConfig.getTAwardPkgId() != null && awardConfig.getTAwardPkgId() > 0) {
                // 发放指定奖品包
                BatchWelfareResult result = hdztAwardServiceClient.doWelfare(
                    time,
                    attr.getAwardBusiId(),
                    anchorUid,
                    awardConfig.getTAwardTskId(),
                    awardConfig.getNum(),
                    awardConfig.getTAwardPkgId(),
                    seq,
                    Maps.newHashMap()
                );

                log.info("Distribute award to anchor result: uid:{}, result:{}", anchorUid, result);
            } else {
                // 抽奖模式
                log.info("Award config is lottery mode for anchor:{}, taskId:{}", anchorUid, awardConfig.getTAwardTskId());
                // TODO: 实现抽奖逻辑
            }

        } catch (Exception e) {
            log.error("Failed to distribute award to anchor:{}", anchorUid, e);
        }
    }

    /**
     * 发送应援口令到房间
     * TODO: 调用ChannelWatchwordLotteryComponent的具体方法
     */
    private void sendWatchwordToRoom(Long userUid, Long anchorUid, CpHourlyWinnerComponentAttr attr) {
        try {
            // 获取第一名CP所在房间信息
            Cmpt2061LatestCp latestCp = latestCpComponent.getLatestCp(
                attr.getActId(),
                attr.getLatestCpComponentIndex(),
                "", // 不限制日期
                attr.getCpRankId(),
                anchorUid
            );

            if (latestCp == null) {
                log.warn("Cannot find latest cp room info for anchorUid:{}", anchorUid);
                return;
            }

            // 调用ChannelWatchwordLotteryComponent的具体方法
            // 生成唯一序列号
            String seq = String.format("cp_hourly_winner_%d_%d_%s_%d_%d_%d",
                attr.getActId(),
                attr.getCmptUseInx(),
                DateUtil.getNowYyyyMMddHH(),
                anchorUid,
                latestCp.getSid(),
                latestCp.getSsid());

            // 构建CP成员ID
            String memberId = String.format("%d_%d", userUid, anchorUid);

            // 设置过期时间
            Date expiredTime = DateUtil.addSeconds(new Date(), attr.getWatchwordExpireSeconds().intValue());

            // 调用ChannelWatchwordLotteryComponent添加应援口令抽奖
            channelWatchwordLotteryComponent.addWatchwordLotteryBox(
                attr.getActId(),
                attr.getWatchwordLotteryComponentIndex(),
                seq,
                memberId,
                latestCp.getSid(),
                latestCp.getSsid(),
                expiredTime
            );

            log.info("sendWatchwordToRoom success, userUid:{}, anchorUid:{}, seq:{}", userUid, anchorUid, seq);
        } catch (Exception e) {
            log.error("发送应援口令到房间失败, userUid:{}, anchorUid:{}", userUid, anchorUid, e);
        }
    }

    /**
     * 全服广播第一名CP信息
     * 实现广播逻辑
     */
    private void broadcastWinnerInfo(Long userUid, Long anchorUid, Long cpScore, CpHourlyWinnerComponentAttr attr) {
        try {
            // 获取用户和主播信息
            Map<Long, UserInfoVo> userInfoMap = userInfoService.getUserInfo(List.of(userUid, anchorUid), Template.unknown);
            UserInfoVo userInfo = userInfoMap.get(userUid);
            UserInfoVo anchorInfo = userInfoMap.get(anchorUid);

            if (userInfo == null || anchorInfo == null) {
                log.warn("Failed to get user info for broadcast, userUid:{}, anchorUid:{}", userUid, anchorUid);
                return;
            }

            // 获取多昵称信息
            Map<String, Map<String, MultiNickItem>> multiNickUsers = userInfoService.batchGetMultiNickUsers(List.of(userUid, anchorUid));

            // 构建广播数据
            Map<String, Object> broadcastData = Maps.newHashMap();
            broadcastData.put("userUid", userUid);
            broadcastData.put("userNick", userInfo.getNick());
            broadcastData.put("userAvatar", userInfo.getAvatarUrl());
            broadcastData.put("anchorUid", anchorUid);
            broadcastData.put("anchorNick", anchorInfo.getNick());
            broadcastData.put("anchorAvatar", anchorInfo.getAvatarUrl());
            broadcastData.put("cpScore", cpScore);
            broadcastData.put("hourCode", DateUtil.getNowYyyyMMddHH());

            // 添加多昵称信息
            if (multiNickUsers != null) {
                broadcastData.put("userMultiNick", JSON.toJSONString(multiNickUsers.get(userUid.toString())));
                broadcastData.put("anchorMultiNick", JSON.toJSONString(multiNickUsers.get(anchorUid.toString())));
            }

            // 调用广播服务
            commonBroadCastService.commonBannerBroadcast(
                0, 0, 0, Template.unknown, attr.getBroadcastType(),
                attr.getActId(), userUid, cpScore,
                attr.getBroadcastBannerId(), attr.getBroadcastBannerType(),
                broadcastData
            );

            log.info("broadcastWinnerInfo completed successfully, userUid:{}, anchorUid:{}, cpScore:{}",
                    userUid, anchorUid, cpScore);
        } catch (Exception e) {
            log.error("全服广播第一名CP信息失败, userUid:{}, anchorUid:{}, cpScore:{}", userUid, anchorUid, cpScore, e);
        }
    }

    /**
     * 查询历史小时冠军CP接口
     */
    @RequestMapping("/queryHistoryWinners")
    public Response<List<HistoryWinnerInfo>> queryHistoryWinners(
            @RequestParam Long actId,
            @RequestParam Long cmptInx,
            @RequestParam String date) {
        
        try {
            // TODO: 实现查询历史冠军逻辑
            log.info("queryHistoryWinners TODO, actId:{}, cmptInx:{}, date:{}", actId, cmptInx, date);
            return Response.success(List.of());
        } catch (Exception e) {
            log.error("查询历史小时冠军失败, actId:{}, cmptInx:{}, date:{}", actId, cmptInx, date, e);
            return Response.fail("查询失败");
        }
    }

    /**
     * 查询全服礼物奖池余额接口
     */
    @RequestMapping("/queryPoolBalance")
    public Response<PoolBalanceInfo> queryPoolBalance(
            @RequestParam Long actId,
            @RequestParam Long cmptInx) {
        
        try {
            // TODO: 实现查询奖池余额逻辑
            log.info("queryPoolBalance TODO, actId:{}, cmptInx:{}", actId, cmptInx);
            
            PoolBalanceInfo balanceInfo = new PoolBalanceInfo();
            balanceInfo.setBalance(0L);
            return Response.success(balanceInfo);
        } catch (Exception e) {
            log.error("查询全服礼物奖池余额失败, actId:{}, cmptInx:{}", actId, cmptInx, e);
            return Response.fail("查询失败");
        }
    }

    /**
     * 历史冠军信息
     */
    @Data
    public static class HistoryWinnerInfo {
        private String userNick;
        private String userAvatar;
        private String anchorNick;
        private String anchorAvatar;
        private String hourCode;
        private Long cpScore;
    }

    /**
     * 奖池余额信息
     */
    @Data
    public static class PoolBalanceInfo {
        private Long balance; // 奖池余额，单位：厘
    }
}
