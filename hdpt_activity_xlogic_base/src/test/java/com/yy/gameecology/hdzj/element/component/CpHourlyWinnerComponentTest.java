package com.yy.gameecology.hdzj.element.component;

import com.yy.gameecology.activity.bean.hdzt.RankingTimeEnd;
import com.yy.gameecology.hdzj.element.component.attr.CpHourlyWinnerComponentAttr;
import com.yy.gameecology.hdzj.element.component.attr.bean.AwardAttrConfig;
import com.yy.gameecology.hdzj.element.component.dao.CpHourlyWinnerDao;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * CP小时第一名玩法组件测试类
 * 
 * <AUTHOR> Generated
 * @date 2025-07-23
 */
@ExtendWith(MockitoExtension.class)
class CpHourlyWinnerComponentTest {
    
    @Mock
    private CpHourlyWinnerDao cpHourlyWinnerDao;
    
    @InjectMocks
    private CpHourlyWinnerComponent cpHourlyWinnerComponent;
    
    private CpHourlyWinnerComponentAttr attr;
    private RankingTimeEnd event;
    
    @BeforeEach
    void setUp() {
        // 初始化组件属性
        attr = new CpHourlyWinnerComponentAttr();
        attr.setActId(12345L);
        attr.setCmptUseInx(1L);
        attr.setCpRankId(100L);
        attr.setCpPhaseId(200L);
        attr.setAwardBusiId(810L);
        attr.setGiftPoolTotalLimit(4000000L);
        attr.setWatchwordLotteryComponentIndex(1L);
        attr.setLatestCpComponentIndex(2L);
        attr.setEnableBroadcast(1);
        attr.setEnableWatchword(1);
        
        // 配置分数区间奖励
        Map<Long, Map<Long, AwardAttrConfig>> scoreRangeAwards = new HashMap<>();
        Map<Long, AwardAttrConfig> range1 = new HashMap<>();
        AwardAttrConfig award1 = new AwardAttrConfig();
        award1.setTAwardTskId(100L);
        award1.setTAwardPkgId(200L);
        award1.setNum(1);
        award1.setAwardAmount(10000L);
        range1.put(5000L, award1);
        scoreRangeAwards.put(1000L, range1);
        attr.setScoreRangeAwards(scoreRangeAwards);
        
        // 初始化事件
        event = new RankingTimeEnd();
        event.setActId(12345L);
        event.setRankId(100L);
        event.setPhaseId(200L);
        event.setTimeKey(2L); // 按小时分榜
        event.setEndTime("2025-07-23 15:00:00");
        event.setSeq("test_seq_123");
    }
    
    @Test
    void testGetComponentId() {
        assertEquals(5159L, cpHourlyWinnerComponent.getComponentId());
    }
    
    @Test
    void testIsMyDuty() {
        assertTrue(attr.isMyDuty(100L, 200L));
        assertFalse(attr.isMyDuty(101L, 200L));
        assertFalse(attr.isMyDuty(100L, 201L));
    }
    
    @Test
    void testGetAwardByScore() {
        // 测试分数在区间内
        AwardAttrConfig award = attr.getAwardByScore(3000L);
        assertNotNull(award);
        assertEquals(100L, award.getTAwardTskId());
        assertEquals(10000L, award.getAwardAmount());
        
        // 测试分数超出区间
        AwardAttrConfig award2 = attr.getAwardByScore(6000L);
        assertNull(award2);
        
        // 测试分数低于最小区间
        AwardAttrConfig award3 = attr.getAwardByScore(500L);
        assertNull(award3);
    }
    
    @Test
    void testOnRankingTimeEnd_NotMyDuty() {
        // 修改事件的榜单ID，使其不属于当前组件负责
        event.setRankId(999L);
        
        // 调用事件处理方法
        cpHourlyWinnerComponent.onRankingTimeEnd(event, attr);
        
        // 验证没有进行任何数据库操作
        verify(cpHourlyWinnerDao, never()).isEventProcessed(anyLong(), anyLong(), anyString());
    }
    
    @Test
    void testOnRankingTimeEnd_EventAlreadyProcessed() {
        // 模拟事件已经处理过
        when(cpHourlyWinnerDao.isEventProcessed(anyLong(), anyLong(), anyString())).thenReturn(true);
        
        // 调用事件处理方法
        cpHourlyWinnerComponent.onRankingTimeEnd(event, attr);
        
        // 验证检查了事件是否已处理
        verify(cpHourlyWinnerDao).isEventProcessed(attr.getActId(), attr.getCmptUseInx(), "test_seq_123");
    }
    
    @Test
    void testOnRankingTimeEnd_NewEvent() {
        // 模拟事件未处理过
        when(cpHourlyWinnerDao.isEventProcessed(anyLong(), anyLong(), anyString())).thenReturn(false);
        
        // 调用事件处理方法（由于依赖的服务都是mock，会在查询榜单时出现异常，但这里主要测试幂等性检查）
        try {
            cpHourlyWinnerComponent.onRankingTimeEnd(event, attr);
        } catch (Exception e) {
            // 预期会有异常，因为依赖的服务都是mock
        }
        
        // 验证检查了事件是否已处理
        verify(cpHourlyWinnerDao).isEventProcessed(attr.getActId(), attr.getCmptUseInx(), "test_seq_123");
    }
    
    @Test
    void testQueryHistoryWinners() {
        // 测试查询历史冠军接口
        var response = cpHourlyWinnerComponent.queryHistoryWinners(12345L, 1L, "20250723");
        
        // 验证返回成功（虽然数据为null，因为是TODO状态）
        assertNotNull(response);
        assertEquals(0, response.getResult());
    }
    
    @Test
    void testQueryGiftPoolBalance() {
        // 测试查询奖池余额接口
        var response = cpHourlyWinnerComponent.queryGiftPoolBalance(12345L, 1L);
        
        // 验证返回成功（虽然数据为null，因为是TODO状态）
        assertNotNull(response);
        assertEquals(0, response.getResult());
    }
    
    @Test
    void testScoreRangeAwardsConfiguration() {
        // 测试复杂的分数区间配置
        Map<Long, Map<Long, AwardAttrConfig>> complexAwards = new HashMap<>();
        
        // 第一个区间：1000-5000分
        Map<Long, AwardAttrConfig> range1 = new HashMap<>();
        AwardAttrConfig award1 = new AwardAttrConfig();
        award1.setAwardAmount(5000L);
        range1.put(5000L, award1);
        complexAwards.put(1000L, range1);
        
        // 第二个区间：5001-10000分
        Map<Long, AwardAttrConfig> range2 = new HashMap<>();
        AwardAttrConfig award2 = new AwardAttrConfig();
        award2.setAwardAmount(10000L);
        range2.put(10000L, award2);
        complexAwards.put(5001L, range2);
        
        // 第三个区间：10001分以上（maxScore为0表示无上限）
        Map<Long, AwardAttrConfig> range3 = new HashMap<>();
        AwardAttrConfig award3 = new AwardAttrConfig();
        award3.setAwardAmount(20000L);
        range3.put(0L, award3);
        complexAwards.put(10001L, range3);
        
        attr.setScoreRangeAwards(complexAwards);
        
        // 测试各个分数区间
        assertEquals(5000L, attr.getAwardByScore(3000L).getAwardAmount());
        assertEquals(10000L, attr.getAwardByScore(7000L).getAwardAmount());
        assertEquals(20000L, attr.getAwardByScore(15000L).getAwardAmount());
        assertNull(attr.getAwardByScore(500L)); // 低于最小区间
    }
}
