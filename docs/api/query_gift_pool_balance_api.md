# 查询全服礼物奖池余额接口文档

## 接口概述

**接口名称**: 查询全服礼物奖池余额  
**接口路径**: `GET /5159/queryGiftPoolBalance`  
**功能描述**: 查询指定活动和组件的礼物奖池余额信息，包括总限额、已使用金额、剩余余额等

## 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| actId | Long | 是 | - | 活动ID |
| cmptUseInx | Long | 是 | - | 组件使用索引 |
| includeDetail | Boolean | 否 | false | 是否包含详细统计信息 |

## 请求示例

```bash
# 查询基础余额信息
GET /5159/queryGiftPoolBalance?actId=12345&cmptUseInx=1

# 查询包含详细统计的余额信息
GET /5159/queryGiftPoolBalance?actId=12345&cmptUseInx=1&includeDetail=true
```

## 响应格式

### 基础响应（includeDetail=false）

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "balance": {
      "totalLimit": 4000000,
      "totalLimitYuan": 40000.0,
      "currentUsed": 1296000,
      "currentUsedYuan": 12960.0,
      "remainingBalance": 2704000,
      "remainingBalanceYuan": 27040.0,
      "usagePercentage": 32.4,
      "isPoolSufficient": true
    },
    "config": {
      "giftPoolDataKey": "CP_GIFT_POOL_LIMIT",
      "poolInsufficientAward": {
        "tAwardTskId": 100003,
        "tAwardPkgId": 200003,
        "awardName": "夏日飞骏进场秀3天"
      }
    },
    "queryTime": 1690012800000,
    "queryTimeStr": "20250722145959"
  }
}
```

### 详细响应（includeDetail=true）

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "balance": {
      "totalLimit": 4000000,
      "totalLimitYuan": 40000.0,
      "currentUsed": 1296000,
      "currentUsedYuan": 12960.0,
      "remainingBalance": 2704000,
      "remainingBalanceYuan": 27040.0,
      "usagePercentage": 32.4,
      "isPoolSufficient": true
    },
    "config": {
      "giftPoolDataKey": "CP_GIFT_POOL_LIMIT",
      "poolInsufficientAward": {
        "tAwardTskId": 100003,
        "tAwardPkgId": 200003,
        "awardName": "夏日飞骏进场秀3天"
      }
    },
    "detail": {
      "todayStats": {
        "date": "20250722",
        "winnerCount": 15,
        "poolUsed": 75000,
        "poolUsedYuan": 750.0,
        "insufficientCount": 0,
        "sufficientRate": 100.0
      },
      "recentDays": [
        {
          "date": "20250722",
          "winnerCount": 15,
          "poolUsed": 75000,
          "poolUsedYuan": 750.0
        },
        {
          "date": "20250721",
          "winnerCount": 24,
          "poolUsed": 120000,
          "poolUsedYuan": 1200.0
        }
      ],
      "poolAnalysis": {
        "avgAwardAmount": 5000.0,
        "avgAwardAmountYuan": 50.0,
        "estimatedRemainTimes": 540,
        "warningThreshold": 400000,
        "warningThresholdYuan": 4000.0,
        "isNearWarning": false,
        "isCritical": false
      }
    },
    "queryTime": 1690012800000,
    "queryTimeStr": "20250722145959"
  }
}
```

### 错误响应

```json
{
  "code": 400,
  "msg": "参数不能为空",
  "data": null
}
```

```json
{
  "code": 404,
  "msg": "组件配置不存在",
  "data": null
}
```

```json
{
  "code": 500,
  "msg": "查询失败: 数据库连接异常",
  "data": null
}
```

## 响应字段说明

### balance对象字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| totalLimit | Long | 奖池总限额（厘） |
| totalLimitYuan | Double | 奖池总限额（元） |
| currentUsed | Long | 当前已使用金额（厘） |
| currentUsedYuan | Double | 当前已使用金额（元） |
| remainingBalance | Long | 剩余余额（厘） |
| remainingBalanceYuan | Double | 剩余余额（元） |
| usagePercentage | Double | 使用百分比 |
| isPoolSufficient | Boolean | 奖池是否充足 |

### config对象字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| giftPoolDataKey | String | 奖池数据键名 |
| poolInsufficientAward | Object | 奖池不足时的替代奖励配置 |

### detail对象字段（仅当includeDetail=true时返回）

#### todayStats对象

| 字段名 | 类型 | 说明 |
|--------|------|------|
| date | String | 日期（yyyyMMdd） |
| winnerCount | Long | 今日冠军数量 |
| poolUsed | Long | 今日奖池使用金额（厘） |
| poolUsedYuan | Double | 今日奖池使用金额（元） |
| insufficientCount | Long | 今日奖池不足次数 |
| sufficientRate | Double | 今日奖池充足率（%） |

#### recentDays数组元素

| 字段名 | 类型 | 说明 |
|--------|------|------|
| date | String | 日期（yyyyMMdd） |
| winnerCount | Long | 当日冠军数量 |
| poolUsed | Long | 当日奖池使用金额（厘） |
| poolUsedYuan | Double | 当日奖池使用金额（元） |

#### poolAnalysis对象

| 字段名 | 类型 | 说明 |
|--------|------|------|
| avgAwardAmount | Double | 平均奖励金额（厘） |
| avgAwardAmountYuan | Double | 平均奖励金额（元） |
| estimatedRemainTimes | Long | 预估剩余奖励次数 |
| warningThreshold | Double | 预警阈值（厘） |
| warningThresholdYuan | Double | 预警阈值（元） |
| isNearWarning | Boolean | 是否接近预警 |
| isCritical | Boolean | 是否严重预警 |

## 业务逻辑

### 查询逻辑

1. **参数验证**：验证必填参数不为空
2. **组件配置获取**：获取组件属性配置
3. **奖池使用情况查询**：使用CommonDataDao查询当前使用金额
4. **余额计算**：计算剩余余额和使用百分比
5. **详细信息统计**（可选）：统计今日和近7天的使用情况

### 详细信息统计

1. **今日统计**：
   - 冠军数量
   - 奖池使用金额
   - 奖池不足次数
   - 奖池充足率

2. **近7天统计**：
   - 每日冠军数量
   - 每日奖池使用金额

3. **奖池分析**：
   - 平均奖励金额
   - 预估剩余奖励次数
   - 预警状态分析

### 预警机制

- **预警阈值**：剩余余额 ≤ 总限额的10%
- **严重预警**：剩余余额 ≤ 总限额的5%

## 使用示例

### JavaScript调用示例

```javascript
// 查询基础余额信息
fetch('/5159/queryGiftPoolBalance?actId=12345&cmptUseInx=1')
  .then(response => response.json())
  .then(data => {
    if (data.code === 0) {
      const balance = data.data.balance;
      console.log(`奖池余额: ${balance.remainingBalanceYuan}元`);
      console.log(`使用率: ${balance.usagePercentage}%`);
      
      if (!balance.isPoolSufficient) {
        console.warn('奖池余额不足！');
      }
    }
  });

// 查询详细信息
fetch('/5159/queryGiftPoolBalance?actId=12345&cmptUseInx=1&includeDetail=true')
  .then(response => response.json())
  .then(data => {
    if (data.code === 0) {
      const detail = data.data.detail;
      console.log(`今日冠军数: ${detail.todayStats.winnerCount}`);
      console.log(`今日使用: ${detail.todayStats.poolUsedYuan}元`);
      
      if (detail.poolAnalysis.isNearWarning) {
        console.warn('奖池余额接近预警线！');
      }
    }
  });
```

### Java调用示例

```java
// 使用RestTemplate调用
String url = "/5159/queryGiftPoolBalance?actId=12345&cmptUseInx=1&includeDetail=true";
ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);

if (response.getStatusCode().is2xxSuccessful()) {
    Map<String, Object> data = (Map<String, Object>) response.getBody().get("data");
    Map<String, Object> balance = (Map<String, Object>) data.get("balance");
    
    Double remainingYuan = (Double) balance.get("remainingBalanceYuan");
    Boolean isPoolSufficient = (Boolean) balance.get("isPoolSufficient");
    
    System.out.println("奖池余额: " + remainingYuan + "元");
    
    if (!isPoolSufficient) {
        System.out.println("警告：奖池余额不足！");
    }
}
```

## 监控建议

### 关键指标监控

1. **余额监控**：
   - 剩余余额 < 总限额的10%时发送预警
   - 剩余余额 < 总限额的5%时发送严重预警

2. **使用率监控**：
   - 日使用率异常增长时预警
   - 小时使用率超过预期时预警

3. **奖池不足监控**：
   - 奖池不足次数增加时预警
   - 连续奖池不足时严重预警

### 运营建议

1. **定期检查**：建议每日检查奖池余额
2. **预警响应**：接近预警线时及时补充奖池或调整奖励策略
3. **数据分析**：定期分析奖池使用趋势，优化奖励配置

## 注意事项

1. **数据一致性**：查询结果基于数据库当前状态
2. **缓存策略**：建议对奖池余额进行适当缓存，避免频繁查询
3. **权限控制**：接口需要相应的访问权限
4. **性能考虑**：详细信息查询会增加响应时间，按需使用
