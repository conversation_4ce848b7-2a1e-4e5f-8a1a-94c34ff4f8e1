# UserInfo Service API 文档

## 服务类全名
`com.yy.gameecology.activity.service.UserInfoService`

## 方法说明

### 1. getUserInfo 方法
**功能**：获取单个用户信息

**参数**：
- `uid` (long): 用户ID

**返回类型**：`UserInfoVo` (用户信息对象)

**使用示例**：
```java
UserInfoVo user = userInfoService.getUserInfo(12345L);
```

### 2. getUserInfo (批量) 方法
**功能**：批量获取用户信息

**参数**：
- `uids` (Collection<Long>): 用户ID列表

**返回类型**：`Map<Long, UserInfoVo>` (用户信息映射表)

**使用示例**：
```java
Map<Long, UserInfoVo> users = userInfoService.getUserInfo(Arrays.asList(12345L, 67890L));
```

### 3. getUserInfoWithNickExt 方法（多昵称版本）
**功能**：获取带扩展昵称的用户信息，支持多平台昵称获取

**方法重载版本**：
1. `getUserInfoWithNickExt(List<Long> uids, Map<String, Map<String, MultiNickItem>> multiNickUsers, boolean nickBase64, int templateType)` - 完整版本
2. `getUserInfoWithNickExt(List<Long> uids, Map<String, Map<String, MultiNickItem>> multiNickUsers)` - 简化版本
3. `getUserInfoWithNickExt(List<Long> uids, Map<String, Map<String, MultiNickItem>> multiNickUsers, int templateType)` - 指定模板版本

**完整版本参数**：
- `uids` (List<Long>): 用户ID列表
- `multiNickUsers` (Map<String, Map<String, MultiNickItem>>): 用于接收多昵称扩展数据的容器，方法执行后会填充数据
- `nickBase64` (boolean): 是否对昵称进行Base64编码
- `templateType` (int): 模板类型代码，参考Template枚举

**返回类型**：`Map<Long, UserInfoVo>` (用户信息映射表)

**核心特性**：
- 自动分批处理：单次最大查询499个用户，超出会自动分批
- 多平台昵称：支持获取用户在不同平台（YY、百度、贴吧等）的昵称
- Base64编码：可选择对昵称进行Base64编码处理
- 模板支持：根据不同业务模板获取对应的用户信息

```