# 查询历史小时冠军CP接口文档

## 接口概述

**接口名称**: 查询历史小时冠军CP  
**接口路径**: `GET /5159/queryHistoryWinners`  
**功能描述**: 查询指定活动和组件的历史小时冠军CP记录

## 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| actId | Long | 是 | - | 活动ID |
| cmptUseInx | Long | 是 | - | 组件使用索引 |
| dateCode | String | 是 | - | 日期编码，支持两种格式 |
| limit | Integer | 否 | 24 | 查询条数限制，最大100 |

### dateCode格式说明

1. **yyyyMMdd格式**（8位）：查询指定日期的所有小时冠军
   - 示例：`20250722` - 查询2025年7月22日的所有小时冠军
   
2. **yyyyMMddHH格式**（10位）：查询指定小时及之前的冠军记录
   - 示例：`2025072214` - 查询2025年7月22日14点及之前的冠军记录

## 请求示例

```bash
# 查询指定日期的所有小时冠军
GET /5159/queryHistoryWinners?actId=12345&cmptUseInx=1&dateCode=20250722&limit=24

# 查询指定小时及之前的冠军记录
GET /5159/queryHistoryWinners?actId=12345&cmptUseInx=1&dateCode=2025072214&limit=10
```

## 响应格式

### 成功响应

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "winners": [
      {
        "hourCode": "2025072214",
        "cpScore": 12345,
        "createTime": "2025-07-22 14:59:59",
        "user": {
          "uid": 123456789,
          "nick": "用户昵称",
          "avatar": "https://example.com/avatar1.jpg",
          "multiNick": {
            "platform1": "昵称1",
            "platform2": "昵称2"
          }
        },
        "anchor": {
          "uid": 987654321,
          "nick": "主播昵称",
          "avatar": "https://example.com/avatar2.jpg",
          "multiNick": {
            "platform1": "主播昵称1",
            "platform2": "主播昵称2"
          }
        },
        "award": {
          "amount": 500000,
          "amountYuan": 5000.0,
          "isPoolSufficient": true,
          "config": {
            "tAwardTskId": 100001,
            "tAwardPkgId": 200001,
            "awardName": "豪华礼包"
          }
        },
        "channel": {
          "sid": 12345,
          "ssid": 67890
        },
        "status": {
          "watchwordSent": true,
          "broadcastSent": true
        }
      }
    ],
    "total": 1,
    "dateCode": "20250722",
    "queryTime": 1690012800000
  }
}
```

### 错误响应

```json
{
  "code": 400,
  "msg": "参数不能为空",
  "data": null
}
```

```json
{
  "code": 400,
  "msg": "日期格式错误，支持格式：yyyyMMdd 或 yyyyMMddHH",
  "data": null
}
```

```json
{
  "code": 500,
  "msg": "查询失败: 数据库连接异常",
  "data": null
}
```

## 响应字段说明

### 主要字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| winners | Array | 冠军CP列表 |
| total | Integer | 返回记录总数 |
| dateCode | String | 查询的日期编码 |
| queryTime | Long | 查询时间戳 |

### winners数组元素字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| hourCode | String | 小时编码（yyyyMMddHH） |
| cpScore | Long | CP分数 |
| createTime | String | 记录创建时间 |
| user | Object | 用户信息 |
| anchor | Object | 主播信息 |
| award | Object | 奖励信息（可选） |
| channel | Object | 房间信息（可选） |
| status | Object | 状态信息 |

### user/anchor对象字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| uid | Long | 用户/主播UID |
| nick | String | 昵称 |
| avatar | String | 头像URL |
| multiNick | Object | 多平台昵称（可选） |

### award对象字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| amount | Long | 奖励金额（厘） |
| amountYuan | Double | 奖励金额（元） |
| isPoolSufficient | Boolean | 奖池是否充足 |
| config | Object | 奖励配置信息 |

### channel对象字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| sid | Long | 房间SID |
| ssid | Long | 房间SSID |

### status对象字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| watchwordSent | Boolean | 是否已发送应援口令 |
| broadcastSent | Boolean | 是否已广播 |

## 业务逻辑

### 查询逻辑

1. **参数验证**：
   - 验证必填参数不为空
   - 验证limit范围（1-100）
   - 验证dateCode格式

2. **查询策略**：
   - **8位日期码**：使用前缀匹配查询整天数据
   - **10位日期码**：使用范围查询从当天00点到指定小时

3. **数据组装**：
   - 解析多昵称JSON数据
   - 计算奖励金额的元单位
   - 组装完整的响应结构

### 错误处理

1. **参数错误**：返回400状态码和具体错误信息
2. **数据库异常**：返回500状态码和错误描述
3. **JSON解析异常**：记录警告日志，不影响主流程

### 性能优化

1. **查询限制**：最大查询100条记录
2. **索引利用**：利用数据库索引提高查询效率
3. **异常容错**：JSON解析失败不影响其他数据返回

## 使用示例

### JavaScript调用示例

```javascript
// 查询今天的所有小时冠军
fetch('/5159/queryHistoryWinners?actId=12345&cmptUseInx=1&dateCode=20250722')
  .then(response => response.json())
  .then(data => {
    if (data.code === 0) {
      console.log('查询成功:', data.data.winners);
    } else {
      console.error('查询失败:', data.msg);
    }
  });

// 查询指定小时的冠军
fetch('/5159/queryHistoryWinners?actId=12345&cmptUseInx=1&dateCode=2025072214&limit=1')
  .then(response => response.json())
  .then(data => {
    if (data.code === 0 && data.data.winners.length > 0) {
      const winner = data.data.winners[0];
      console.log(`${winner.hourCode}小时冠军: ${winner.user.nick} & ${winner.anchor.nick}`);
    }
  });
```

### Java调用示例

```java
// 使用RestTemplate调用
String url = "/5159/queryHistoryWinners?actId=12345&cmptUseInx=1&dateCode=20250722";
ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);

if (response.getStatusCode().is2xxSuccessful()) {
    Map<String, Object> data = (Map<String, Object>) response.getBody().get("data");
    List<Map<String, Object>> winners = (List<Map<String, Object>>) data.get("winners");
    
    for (Map<String, Object> winner : winners) {
        System.out.println("小时冠军: " + winner.get("hourCode"));
    }
}
```

## 注意事项

1. **时区处理**：所有时间均为服务器本地时间
2. **数据一致性**：查询结果基于数据库当前状态
3. **缓存策略**：建议客户端对历史数据进行适当缓存
4. **权限控制**：接口需要相应的访问权限
5. **频率限制**：建议控制调用频率，避免对数据库造成压力
