# CommonBroadCastService API 文档

## 类全名
`com.yy.gameecology.activity.service.CommonBroadCastService`

## 常用方法说明

### 横幅广播方法
```
/**
 * @param familyId 家族ID，如果需要广播家族下的所有厅，则需要填写
 * @param broType  broType 2-子频道广播 3-顶级频道下所有子厅广播/家族下面所有子厅 4-全模板
 */
public void commonBannerBroadcast(long sid, long ssid, long familyId, Template template, long broType,
                                  long actId, long uid, long score, long bannerId, long bannerType, Object jsonMap)
```

### 单播通知方法 通用弹窗单播方法
```
/**
 * 通用弹窗单播
 *
 * @param actId       活动id
 * @param noticeType  通知类型,需要和前端协定好
 * @param noticeValue 通知数据,需要和前端协定好
 * @param noticeExt   扩展数据,需要和前端协定好
 * @param uid         用户id
 **/
public void commonNoticeUnicast(long actId, String noticeType, String noticeValue, String noticeExt, long uid)
```