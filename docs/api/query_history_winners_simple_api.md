# 查询历史小时冠军CP接口文档（简化版）

## 接口概述

**接口名称**: 查询历史小时冠军CP  
**接口路径**: `GET /5159/queryHistoryWinners`  
**功能描述**: 查询指定活动、组件和日期的历史小时冠军CP记录

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| actId | Long | 是 | 活动ID |
| cmptUseInx | Long | 是 | 组件索引 |
| dateCode | String | 是 | 日期编码 |

## 请求示例

```bash
GET /5159/queryHistoryWinners?actId=12345&cmptUseInx=1&dateCode=20250722
```

## 响应格式

### 成功响应

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "winners": [
      {
        "userNick": "用户昵称1",
        "userAvatar": "https://example.com/avatar1.jpg",
        "anchorNick": "主播昵称1",
        "anchorAvatar": "https://example.com/anchor1.jpg"
      },
      {
        "userNick": "用户昵称2",
        "userAvatar": "https://example.com/avatar2.jpg",
        "anchorNick": "主播昵称2",
        "anchorAvatar": "https://example.com/anchor2.jpg"
      }
    ]
  }
}
```

### 错误响应

```json
{
  "code": 500,
  "msg": "查询失败",
  "data": null
}
```

## 响应字段说明

### winners数组元素字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| userNick | String | 用户昵称 |
| userAvatar | String | 用户头像URL |
| anchorNick | String | 主播昵称 |
| anchorAvatar | String | 主播头像URL |

## 实现逻辑

严格按照方法注释要求实现：

1. **根据dateCode查询历史冠军记录**
   - 使用`cpHourlyWinnerComponentDao.queryHistoryWinnersByPrefix()`查询
   - 传入活动ID、组件索引、日期编码和查询限制

2. **组装返回数据，包括用户昵称、用户头像、主播昵称、主播头像**
   - 遍历查询结果
   - 提取每条记录的用户昵称、用户头像、主播昵称、主播头像
   - 组装成标准的返回格式

## 核心代码逻辑

```java
// 1. 根据dateCode查询历史冠军记录
List<Cmpt5159CpHourlyWinner> winnerRecords = cpHourlyWinnerComponentDao.queryHistoryWinnersByPrefix(
    actId, cmptUseInx, dateCode, 100);

// 2. 组装返回数据，包括用户昵称、用户头像、主播昵称、主播头像
List<Map<String, Object>> winners = new ArrayList<>();
for (Cmpt5159CpHourlyWinner record : winnerRecords) {
    Map<String, Object> winner = new HashMap<>();
    winner.put("userNick", record.getUserNick());
    winner.put("userAvatar", record.getUserAvatar());
    winner.put("anchorNick", record.getAnchorNick());
    winner.put("anchorAvatar", record.getAnchorAvatar());
    winners.add(winner);
}

Map<String, Object> result = new HashMap<>();
result.put("winners", winners);
```

## 使用示例

### JavaScript调用

```javascript
fetch('/5159/queryHistoryWinners?actId=12345&cmptUseInx=1&dateCode=20250722')
  .then(response => response.json())
  .then(data => {
    if (data.code === 0) {
      const winners = data.data.winners;
      winners.forEach(winner => {
        console.log(`CP: ${winner.userNick} & ${winner.anchorNick}`);
      });
    } else {
      console.error('查询失败:', data.msg);
    }
  });
```

### Java调用

```java
String url = "/5159/queryHistoryWinners?actId=12345&cmptUseInx=1&dateCode=20250722";
ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);

if (response.getStatusCode().is2xxSuccessful()) {
    Map<String, Object> data = (Map<String, Object>) response.getBody().get("data");
    List<Map<String, Object>> winners = (List<Map<String, Object>>) data.get("winners");
    
    for (Map<String, Object> winner : winners) {
        String userNick = (String) winner.get("userNick");
        String anchorNick = (String) winner.get("anchorNick");
        System.out.println("CP: " + userNick + " & " + anchorNick);
    }
}
```

## 数据来源

- **历史记录**: 来自数据库表`cmpt_5159_cp_hourly_winner`
- **查询方法**: 使用`CpHourlyWinnerComponentDao.queryHistoryWinnersByPrefix()`
- **查询限制**: 默认最多返回100条记录

## dateCode参数说明

- **格式**: 支持日期前缀匹配
- **示例**: 
  - `20250722` - 查询2025年7月22日的所有记录
  - `202507` - 查询2025年7月的所有记录
  - `2025` - 查询2025年的所有记录

## 注意事项

1. **数据完整性**: 返回的数据基于数据库中已存储的记录
2. **查询限制**: 默认最多返回100条记录，避免数据量过大
3. **空值处理**: 如果昵称或头像为空，将返回null值
4. **权限控制**: 接口需要相应的访问权限
5. **性能考虑**: 建议对查询结果进行适当缓存

## 错误处理

- **500错误**: 数据库查询异常或其他系统错误
- **参数验证**: 自动验证必填参数
- **空结果**: 如果没有匹配的记录，返回空的winners数组

## 监控建议

- 监控接口响应时间
- 监控查询成功率
- 监控返回数据量
- 记录异常查询日志

## 与原始需求的对应关系

**原始需求**: 
> 接口1. 提供查询历史小时冠军CP接口，传入参数为，活动id、组件索引、日期，返回结果为用户昵称、用户头像、主播昵称、主播头像

**实现对应**:
- ✅ 传入参数: actId（活动id）、cmptUseInx（组件索引）、dateCode（日期）
- ✅ 返回结果: userNick（用户昵称）、userAvatar（用户头像）、anchorNick（主播昵称）、anchorAvatar（主播头像）

完全符合原始需求规范。
