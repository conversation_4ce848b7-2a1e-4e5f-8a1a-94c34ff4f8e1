# 查询全服礼物奖池余额接口文档（简化版）

## 接口概述

**接口名称**: 查询全服礼物奖池余额  
**接口路径**: `GET /5159/queryGiftPoolBalance`  
**功能描述**: 查询指定活动和组件的礼物奖池余额信息

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| actId | Long | 是 | 活动ID |
| cmptUseInx | Long | 是 | 组件索引 |

## 请求示例

```bash
GET /5159/queryGiftPoolBalance?actId=12345&cmptUseInx=1
```

## 响应格式

### 成功响应

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "balance": 2704000,
    "balanceYuan": 27040.0
  }
}
```

### 错误响应

```json
{
  "code": 404,
  "msg": "组件配置不存在",
  "data": null
}
```

```json
{
  "code": 500,
  "msg": "查询失败",
  "data": null
}
```

## 响应字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| balance | Long | 奖池余额（厘） |
| balanceYuan | Double | 奖池余额（元） |

## 实现逻辑

严格按照方法注释要求实现：

1. **使用CommonDataDao查询当前奖池使用情况**
   - 通过`commonDataDao.getValue()`查询当前已使用的奖池金额
   - 使用组件配置中的`giftPoolDataKey`作为数据键

2. **计算剩余余额**
   - 获取组件配置中的总限额`giftPoolTotalLimitLi`
   - 计算剩余余额 = 总限额 - 已使用金额
   - 确保余额不为负数

3. **返回奖池余额信息**
   - 返回剩余余额（厘为单位）
   - 同时提供元为单位的便捷字段

## 核心代码逻辑

```java
// 1. 使用CommonDataDao查询当前奖池使用情况
Long currentUsed = commonDataDao.getValue(
    actId,
    this.getComponentId(),
    cmptUseInx,
    attr.getGiftPoolDataKey()
);

// 2. 计算剩余余额
Long totalLimit = attr.getGiftPoolTotalLimitLi();
Long remainingBalance = Math.max(0, totalLimit - currentUsed);

// 3. 返回奖池余额信息
Map<String, Object> result = new HashMap<>();
result.put("balance", remainingBalance);
result.put("balanceYuan", remainingBalance / 100.0);
```

## 使用示例

### JavaScript调用

```javascript
fetch('/5159/queryGiftPoolBalance?actId=12345&cmptUseInx=1')
  .then(response => response.json())
  .then(data => {
    if (data.code === 0) {
      console.log(`奖池余额: ${data.data.balanceYuan}元`);
    } else {
      console.error('查询失败:', data.msg);
    }
  });
```

### Java调用

```java
String url = "/5159/queryGiftPoolBalance?actId=12345&cmptUseInx=1";
ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);

if (response.getStatusCode().is2xxSuccessful()) {
    Map<String, Object> data = (Map<String, Object>) response.getBody().get("data");
    Double balanceYuan = (Double) data.get("balanceYuan");
    System.out.println("奖池余额: " + balanceYuan + "元");
}
```

## 数据来源

- **总限额**: 来自组件属性配置`CpHourlyWinnerComponentAttr.giftPoolTotalLimitLi`
- **已使用金额**: 通过`CommonDataDao`查询，使用`giftPoolDataKey`作为键名
- **数据键**: 默认为`CP_GIFT_POOL_LIMIT`，可在组件属性中配置

## 注意事项

1. **数据单位**: 内部使用厘为单位，避免浮点数精度问题
2. **余额保护**: 确保返回的余额不为负数
3. **配置依赖**: 需要正确配置组件属性中的奖池限额和数据键
4. **权限控制**: 接口需要相应的访问权限
5. **缓存建议**: 可对查询结果进行短期缓存，减少数据库压力

## 错误处理

- **404错误**: 组件配置不存在时返回
- **500错误**: 数据库查询异常或其他系统错误
- **参数验证**: 自动验证必填参数

## 监控建议

- 监控接口响应时间
- 监控查询成功率
- 设置余额预警阈值
- 记录异常查询日志
