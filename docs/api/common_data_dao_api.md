# CommonDataDao API 文档

## 类全名
`com.yy.gameecology.activity.dao.mysql.CommonDataDao`

## 常用方法说明

### 限额的累计和控制 方法
```
/**
 * 【限额表操作】
 * 同1个key,若有重复seq请求，则会返回第一次更新后的value
 *
 * @param actId     活动id
 * @param cmptId
 * @param cmptIndex
 * @param seq
 * @param key       操作表：component_data_limit_${actId}
 * @param value     增量值
 * @return 更新后的值
 */
public long valueIncrIgnore(long actId, long cmptId, long cmptIndex, String seq, String key, long value)
```

### 已累计限额读取
```
/**
 * 【限额表操作】
 *
 * @param actId
 * @param cmptId
 * @param cmptIndex
 * @param key       操作表：component_data_limit_${actId}
 * @return
 */
public long valueGet(long actId, long cmptId, long cmptIndex, String key)
```