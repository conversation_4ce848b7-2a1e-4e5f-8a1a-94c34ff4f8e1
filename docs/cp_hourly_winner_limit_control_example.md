# CP小时第一名玩法组件限额控制使用示例

## 概述

本文档展示了CP小时第一名玩法组件中限额控制功能的使用方式和配置示例。

## 配置示例

### 组件属性配置

```json
{
  "totalAwardLimit": 1000000,     // 总限额：1000元（1000000厘）
  "dailyAwardLimit": 100000,      // 每日限额：100元（100000厘）
  "hourlyAwardLimit": 10000,      // 每小时限额：10元（10000厘）
  "scoreRangeAwards": {
    "1000": {
      "awardAmount": 5000,        // 5元奖励
      "tAwardTskId": 20001,
      "tAwardPkgId": 101,
      "num": 1
    },
    "5000": {
      "awardAmount": 10000,       // 10元奖励
      "tAwardTskId": 20001,
      "tAwardPkgId": 102,
      "num": 1
    }
  },
  "alternativeAward": {
    "awardAmount": 0,             // 替代奖励无金额
    "tAwardTskId": 20001,
    "tAwardPkgId": 103,           // 夏日飞骏进场秀
    "num": 1
  }
}
```

## 限额控制逻辑

### 1. 检查顺序
1. **总限额检查**: 检查整个活动期间累计发放金额
2. **每日限额检查**: 检查当天累计发放金额
3. **每小时限额检查**: 检查当前小时累计发放金额

### 2. 数据库表结构

限额数据存储在`component_data_limit_{actId}`表中：

```sql
-- 总限额记录
key_name: "total_award_limit"
value: 累计发放金额

-- 每日限额记录
key_name: "daily_award_limit_20250723"
value: 当日累计发放金额

-- 每小时限额记录
key_name: "hourly_award_limit_2025072314"
value: 当前小时累计发放金额
```

### 3. 幂等性保证

每个限额检查使用不同的seq后缀：
- 总限额: `{eventSeq}_total`
- 每日限额: `{eventSeq}_daily`
- 每小时限额: `{eventSeq}_hourly`

## 使用场景示例

### 场景1：正常发放
- CP分数：3000分
- 匹配奖励：5元（5000厘）
- 当前累计：总限额50元，日限额20元，时限额5元
- **结果**: 所有限额检查通过，正常发放奖励

### 场景2：超出每日限额
- CP分数：8000分
- 匹配奖励：10元（10000厘）
- 当前累计：总限额50元，日限额95元，时限额5元
- **结果**: 每日限额检查失败（95+10>100），停止发放

### 场景3：超出总限额
- CP分数：8000分
- 匹配奖励：10元（10000厘）
- 当前累计：总限额995元，日限额20元，时限额5元
- **结果**: 总限额检查失败（995+10>1000），停止发放

## 监控和日志

### 限额超出日志示例

```
WARN - Total award limit exceeded, userUid:123456, anchorUid:789012, awardAmount:10000
WARN - Daily award limit exceeded, userUid:123456, anchorUid:789012, awardAmount:10000, dayCode:20250723
WARN - Hourly award limit exceeded, userUid:123456, anchorUid:789012, awardAmount:10000, hourCode:2025072314
```

### 成功发放日志示例

```
INFO - Award distribution completed for userUid:123456, anchorUid:789012, isPoolSufficient:true, awardSuccess:true
```

## 配置建议

### 1. 限额设置原则
- **总限额**: 根据活动预算设置，建议预留10%缓冲
- **每日限额**: 总限额的5-10%，避免单日消耗过快
- **每小时限额**: 每日限额的5-10%，平滑分布

### 2. 监控指标
- 各级限额使用率
- 限额超出频率
- 奖励发放成功率

### 3. 应急处理
- 限额不足时自动切换到替代奖励
- 支持运行时调整限额配置
- 完整的审计日志记录

## 注意事项

1. **幂等性**: 相同事件seq不会重复累计限额
2. **原子性**: 限额检查和奖励发放在同一事务中
3. **性能**: 使用数据库锁确保并发安全
4. **扩展性**: 支持添加更多维度的限额控制
