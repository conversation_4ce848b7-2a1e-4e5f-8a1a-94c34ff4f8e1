实现这个组件
https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/SB5xNxJC_l/4xX96ppH_s/hxt1wNz2QRyrfo


------------------------------------------------------------------------------
------------------------------->第1轮对话<-------------------------------------
------------------------------------------------------------------------------
组件开发分为以下3个步骤：
1、数据库设计

2、组件配置设计规划

3、方法分解、实现

根据component_ai.md中的规范，创建cp小时第一名玩法组件的代码文件，实现以下功能：
------------------------------------------------------------------------------

数据库设计：
1. 设计1个mysql表，用于存储每小时cp榜单的第一名，表中应该包含seq字段，用于约束接口的幂等性

------------------------------------------------------------------------------
程序实现：

榜单结束事件结算方法：
1. 每小时结束时，记录CP榜单第一名。
2. 对第一名CP发放奖励，根据第一分数区间不同，发放不一样的奖励，发奖奖励可配置在组件属性类。
3. 全服礼物奖池总限量40000.0元（当前礼物剩余12960元），若CP获得礼物奖励时，剩余礼物不足，则将以夏日飞骏进场秀3天形式进行发放。奖池限额以及奖池不足时发放的礼物配置，放到组件属性配置。
3. 对第一名CP所在的房间，发送应援口令“夏日派对，浪漫加倍”，可参与抽奖，有机会获得进场秀、头像框等奖励，这个功能的实现，调用ChannelWatchwordLotteryComponent组件实现。
4. 每小时结束时广播第一名CP信息，广播信息包括用户头像、用户昵称、主播头像、主播昵称


http接口：
接口1. 提供查询历史小时冠军CP接口，传入参数为，活动id、组件索引、日期，返回结果为用户昵称、用户头像、主播昵称、主播头像
接口2. 查询全服礼物奖池余额接口，传入参数为，活动id、组件索引，返回结果为奖池余额

实现的时候，只要定义好数据结构、方法定义，对于【榜单结束事件结算方法、http接口1、接口2】的方法主逻辑可以先不实现，可明确注明TODO，以便后面分步骤实现


------------------------------------------------------------------------------
相比上一次解决问题：
包名引入错误减少了

存在问题：
#CpHourlyWinnerComponentAttr中Constant正确的包名应该是com.yy.gameecology.hdzj.element.attrconfig.Constant
TODO--【done】
CpHourlyWinnerComponentAttr中组件属性定义不支持单个非基础类型的数据类型
解决方法：提示词再明确下

#CpHourlyWinnerComponent中eventSeq可优先用ekey
TODO--【done】
解决方法：直接在BaseActComponent定义好这个方法，提示词明确使用这个方法


------------------------------------------------------------------------------
------------------------------->第2轮对话<-------------------------------------
------------------------------------------------------------------------------
请继续完善 private void processHourlyRankSettle(RankingTimeEnd event, CpHourlyWinnerComponentAttr attr) 方法

生成结果比v1、v2的明显要好，有更优雅的方法的封装、发奖等方法更加完善。把大的任务拆小是1个正确的方向

存在问题：
读取用户信息错误：与获取多昵称信息弄混了
------->生成多昵称使用文档，并在主提示词强调--【done】

判断奖池是否充足方法逻辑实现错误
------->限额的累计和控制可以用com.yy.gameecology.activity.dao.mysql.CommonDataDao.valueIncrIgnoreWithLimit方法。--【done】

发送应援口令到房间，AI不知道如何取第一名CP所在的房间
------->第一名cp所在房间的数据可以用LatestCpComponent组件中的getLatestCp方法获取。--【done】

mysql数据保存，没有用到mybatis的mapper，且保存数据没有考虑幂等问题
------->component_ai.md已提到，但没遵守这个规范，在component_ai.md中再强调下，并且在住提示词中提示--【done】









