# CP小时第一名玩法组件

## 组件概述

CP小时第一名玩法组件（ComponentId: 5159）用于实现每小时CP榜单第一名的奖励发放、应援口令、广播等功能。

## 功能特性

### 1. 榜单结束事件结算
- 监听每小时CP榜单结束事件
- 记录第一名CP信息到数据库
- 根据分数区间发放不同奖励
- 处理全服礼物奖池逻辑
- 奖池不足时发放替代奖励

### 2. 应援口令功能
- 向第一名CP所在房间发送应援口令
- 调用ChannelWatchwordLotteryComponent组件实现抽奖

### 3. 广播功能
- 广播第一名CP信息
- 包含用户头像、昵称、主播头像、昵称等信息

### 4. HTTP接口
- 查询历史小时冠军CP接口
- 查询全服礼物奖池余额接口

## 数据库设计

### 表结构：cmpt_5159_cp_hourly_winner

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint(20) | 主键ID |
| act_id | bigint(20) | 活动ID |
| cmpt_use_inx | int(11) | 组件使用索引 |
| seq | varchar(100) | 幂等性序列号 |
| hour_code | varchar(10) | 小时编码（yyyyMMddHH） |
| user_uid | bigint(20) | 用户UID |
| anchor_uid | bigint(20) | 主播UID |
| user_nick | varchar(255) | 用户昵称 |
| anchor_nick | varchar(255) | 主播昵称 |
| user_avatar | varchar(500) | 用户头像 |
| anchor_avatar | varchar(500) | 主播头像 |
| score | bigint(20) | 第一名分数 |
| sid | bigint(20) | 房间SID |
| ssid | bigint(20) | 房间SSID |
| gift_pool_remain | bigint(20) | 奖池剩余金额（分） |
| award_amount | bigint(20) | 实际发放奖励金额（分） |
| award_type | tinyint(4) | 奖励类型：1-礼物奖励，2-进场秀奖励 |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |

## 组件配置

### 主要配置项

```java
@ComponentAttrField(labelText = "CP榜单ID")
private long cpRankId; // 监听的CP榜单ID

@ComponentAttrField(labelText = "分数区间奖励配置")
private Map<String, AwardAttrConfig> scoreRangeAwards; // 分数区间奖励

@ComponentAttrField(labelText = "全服礼物奖池总限额")
private long giftPoolTotalLimit = 4000000L; // 40000元

@ComponentAttrField(labelText = "全服礼物奖池当前余额")
private long giftPoolCurrentBalance = 1296000L; // 12960元

@ComponentAttrField(labelText = "奖池不足时的替代奖励")
private Map<String, AwardAttrConfig> fallbackAwards;

@ComponentAttrField(labelText = "应援口令文案")
private String supportSlogan = "夏日派对，浪漫加倍";

@ComponentAttrField(labelText = "应援口令抽奖组件索引")
private long watchwordLotteryComponentIndex;
```

### 配置示例

```json
{
  "cpRankId": 123456,
  "scoreRangeAwards": {
    "1000-5000": {
      "tAwardTskId": 100001,
      "tAwardPkgId": 0,
      "num": 1,
      "awardName": "小额礼物",
      "awardAmount": 50000
    },
    "5001-10000": {
      "tAwardTskId": 100002,
      "tAwardPkgId": 0,
      "num": 1,
      "awardName": "中等礼物",
      "awardAmount": 100000
    }
  },
  "giftPoolTotalLimit": 4000000,
  "giftPoolCurrentBalance": 1296000,
  "fallbackAwards": {
    "fallback": {
      "tAwardTskId": 100003,
      "tAwardPkgId": 200001,
      "num": 1,
      "awardName": "夏日飞骏进场秀3天"
    },
    "default": {
      "tAwardTskId": 100004,
      "tAwardPkgId": 200002,
      "num": 1,
      "awardName": "默认进场秀3天"
    }
  },
  "supportSlogan": "夏日派对，浪漫加倍",
  "watchwordLotteryComponentIndex": 1,
  "broadcastTemplate": 2,
  "enableBroadcast": true
}
```

## HTTP接口

### 1. 查询历史小时冠军CP

**接口地址**: `GET /cpHourlyWinner/queryHistoryWinners`

**请求参数**:
- `actId`: 活动ID
- `cmptUseInx`: 组件索引
- `dateCode`: 日期编码（支持前缀匹配，如：20250721）

**返回结果**:
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "winners": [
      {
        "userNick": "用户昵称",
        "userAvatar": "用户头像URL",
        "anchorNick": "主播昵称",
        "anchorAvatar": "主播头像URL",
        "hourCode": "2025072114",
        "score": 12345
      }
    ]
  }
}
```

### 2. 查询全服礼物奖池余额

**接口地址**: `GET /cpHourlyWinner/queryGiftPoolBalance`

**请求参数**:
- `actId`: 活动ID
- `cmptUseInx`: 组件索引

**返回结果**:
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "balance": 1296000,
    "balanceYuan": 12960.0
  }
}
```

## 部署步骤

1. **执行数据库建表SQL**:
   ```sql
   -- 执行 docs/sql/cmpt_5159_cp_hourly_winner.sql
   ```

2. **在hdzj_component_define表中添加组件定义**:
   ```sql
   INSERT INTO hdzk.hdzj_component_define (cmpt_id, cmpt_title, remark, author) 
   VALUES (5159, 'CP小时第一名玩法组件', 'CP小时第一名玩法组件', 'AI Generated');
   ```

3. **在活动配置中添加组件**:
   - ComponentId设置为`5159`
   - 配置组件属性，包括榜单ID、奖励配置等

4. **确保依赖组件已配置**:
   - `ChannelWatchwordLotteryComponent`组件已正确配置

## 注意事项

1. **幂等性保证**: 使用seq字段确保同一事件不会重复处理
2. **数据类型**: 涉及金额的字段使用long类型，以分为单位避免浮点数精度问题
3. **奖池管理**: 需要实时更新奖池余额，确保数据一致性
4. **错误处理**: 组件支持重试机制，需要确保业务逻辑的幂等性
5. **依赖组件**: 确保ChannelWatchwordLotteryComponent组件正常工作

## TODO项

当前实现中以下功能标记为TODO，需要后续完善：

1. `processHourlyRankSettle` - 榜单结算主逻辑
2. `queryHistoryWinners` - 历史冠军查询接口实现
3. `queryGiftPoolBalance` - 奖池余额查询接口实现

这些功能的框架已搭建完成，可以分步骤实现具体业务逻辑。
