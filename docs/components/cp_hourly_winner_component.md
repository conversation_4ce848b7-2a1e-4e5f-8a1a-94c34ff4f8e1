# CP小时第一名玩法组件 (组件ID: 5159)

## 概述

CP小时第一名玩法组件用于记录每小时CP榜单的第一名，并对第一名CP进行奖励发放、应援口令发送和全服广播等操作。

## 功能特性

### 1. 数据库设计
- **表名**: `cmpt_5159_cp_hourly_winner`
- **主要字段**:
  - `seq`: 事件序列号，用于幂等性控制
  - `hour_code`: 小时编码，格式：yyyyMMddHH
  - `user_uid`, `anchor_uid`: CP成员UID
  - `cp_score`: CP分数
  - `award_amount`: 奖励金额（单位：厘）
  - `is_pool_sufficient`: 奖池是否充足

### 2. 核心功能

#### 榜单结束事件处理
- 监听 `RankingTimeEnd` 事件
- 记录每小时CP榜单第一名
- 根据分数区间发放不同奖励
- 控制全服礼物奖池限额
- 发送应援口令到房间
- 全服广播第一名CP信息

#### HTTP接口
1. **查询历史小时冠军CP**: `/5159/queryHistoryWinners`
   - 参数: `actId`, `cmptUseInx`, `date`
   - 返回: 用户昵称、头像、主播昵称、头像等信息

2. **查询全服礼物奖池余额**: `/5159/queryGiftPoolBalance`
   - 参数: `actId`, `cmptUseInx`
   - 返回: 奖池余额信息

## 组件配置

### 基础配置
- `cpRankId`: CP榜单ID
- `cpPhaseId`: CP阶段ID
- `awardBusiId`: 发奖业务ID

### 奖励配置
- `scoreRangeAwards`: 分数区间奖励配置
  - 根据CP分数区间配置不同奖励
  - 支持多个分数区间

### 奖池配置
- `giftPoolTotalLimit`: 全服礼物奖池总限额（默认4000000厘，即40000元）
- `poolInsufficientAward`: 奖池不足时发放的礼物配置
- `giftPoolLimitKey`: 奖池限额控制key

### 应援口令配置
- `watchwordLotteryComponentIndex`: 应援口令抽奖组件索引
- `watchwordExpireSeconds`: 应援口令过期时间（默认3600秒）
- `enableWatchword`: 是否启用应援口令（1-启用，0-禁用）

### 广播配置
- `broadcastBannerId`: 广播横幅ID
- `broadcastTemplateType`: 广播模板类型（默认810）
- `broadcastType`: 广播类型（默认5-多业务广播）
- `enableBroadcast`: 是否启用广播（1-启用，0-禁用）

### 其他配置
- `latestCpComponentIndex`: 最新CP组件索引（用于获取CP所在房间信息）

## 实现状态

### 已完成
- [x] 数据库表结构设计
- [x] 实体类和Mapper接口
- [x] 组件属性配置类
- [x] 组件主类框架
- [x] 数据访问层（DAO）
- [x] 事件处理方法框架
- [x] HTTP接口框架

### 待实现（标记为TODO）
- [ ] 榜单结束事件结算方法主逻辑
  - [ ] 记录CP榜单第一名
  - [ ] 对第一名CP发放奖励
  - [ ] 奖池限额控制
  - [ ] 发送应援口令到房间
  - [ ] 全服广播第一名CP信息
- [ ] HTTP接口1：查询历史小时冠军CP
- [ ] HTTP接口2：查询全服礼物奖池余额

## 技术要点

### 幂等性保证
- 使用 `seq` 字段确保事件处理的幂等性
- 数据库使用 `INSERT IGNORE` 语句避免重复插入

### 奖池限额控制
- 使用 `CommonDataDao.valueIncrIgnoreWithLimit` 方法控制限额
- 当奖池不足时，发放配置的替代礼物

### 应援口令集成
- 调用 `ChannelWatchwordLotteryComponent` 组件
- 使用 `LatestCpComponent.getLatestCp` 方法获取房间信息

### 广播功能
- 使用 `CommonBroadCastService` 进行全服广播
- 支持多昵称信息的扩展字段

## 使用示例

### 配置示例
```json
{
  "cpRankId": 12345,
  "cpPhaseId": 67890,
  "awardBusiId": 810,
  "scoreRangeAwards": {
    "1000": {
      "5000": {
        "tAwardTskId": 100,
        "tAwardPkgId": 200,
        "num": 1,
        "awardAmount": 10000
      }
    }
  },
  "giftPoolTotalLimit": 4000000,
  "watchwordLotteryComponentIndex": 1,
  "latestCpComponentIndex": 2,
  "enableBroadcast": 1,
  "enableWatchword": 1
}
```

### API调用示例
```bash
# 查询历史冠军
GET /5159/queryHistoryWinners?actId=123&cmptUseInx=1&date=20250723

# 查询奖池余额
GET /5159/queryGiftPoolBalance?actId=123&cmptUseInx=1
```

## 注意事项

1. **数据库操作**: 遵循 `component_ai.md` 中的代码数据操作要求
2. **幂等性**: 所有事件处理都必须保证幂等性
3. **错误处理**: 所有数据库操作都需要适当的异常处理
4. **日志记录**: 关键操作都需要记录详细日志
5. **配置验证**: 组件启动时需要验证配置的完整性

## 依赖组件

- `ChannelWatchwordLotteryComponent`: 应援口令抽奖功能
- `LatestCpComponent`: 获取CP最新房间信息
- `CommonDataDao`: 数据库操作和限额控制
- `CommonBroadCastService`: 广播服务
- `UserInfoService`: 用户信息服务
