# queryGiftPoolBalance方法严格实现说明

## 实现要求

严格按照方法注释中的要求实现：

```java
/**
 * 查询全服礼物奖池余额接口
 */
// TODO: 实现查询全服礼物奖池余额的具体逻辑
// 1. 使用CommonDataDao查询当前奖池使用情况
// 2. 计算剩余余额
// 3. 返回奖池余额信息
```

## 严格实现内容

### 1. 使用CommonDataDao查询当前奖池使用情况

```java
// 获取组件属性配置
CpHourlyWinnerComponentAttr attr = this.getComponentAttr(actId, cmptUseInx);

// 使用CommonDataDao查询当前奖池使用情况
Long currentUsed = commonDataDao.getValue(
    actId,
    this.getComponentId(),
    cmptUseInx,
    attr.getGiftPoolDataKey()
);

if (currentUsed == null) {
    currentUsed = 0L;
}
```

**实现要点**：
- 使用`commonDataDao.getValue()`方法查询
- 传入活动ID、组件ID、组件使用索引、数据键名
- 处理null值情况，默认为0

### 2. 计算剩余余额

```java
// 获取总限额
Long totalLimit = attr.getGiftPoolTotalLimitLi();

// 计算剩余余额
Long remainingBalance = Math.max(0, totalLimit - currentUsed);
```

**实现要点**：
- 从组件属性获取总限额
- 计算公式：剩余余额 = 总限额 - 已使用金额
- 使用`Math.max(0, ...)`确保余额不为负数

### 3. 返回奖池余额信息

```java
Map<String, Object> result = new HashMap<>();
result.put("balance", remainingBalance); // 剩余余额（厘）
result.put("balanceYuan", remainingBalance / 100.0); // 剩余余额（元）

return Response.success(result);
```

**实现要点**：
- 返回厘为单位的余额（精确计算）
- 同时提供元为单位的便捷字段
- 使用标准的Response格式

## 完整实现代码

```java
@GetMapping("/queryGiftPoolBalance")
public Response<Map<String, Object>> queryGiftPoolBalance(
        @RequestParam("actId") Long actId,
        @RequestParam("cmptUseInx") Long cmptUseInx) {
    
    log.info("queryGiftPoolBalance start, actId:{}, cmptUseInx:{}", actId, cmptUseInx);
    
    try {
        // 1. 使用CommonDataDao查询当前奖池使用情况
        CpHourlyWinnerComponentAttr attr = this.getComponentAttr(actId, cmptUseInx);
        if (attr == null) {
            log.warn("Component attr not found, actId:{}, cmptUseInx:{}", actId, cmptUseInx);
            return Response.fail(404, "组件配置不存在");
        }
        
        // 使用CommonDataDao查询当前奖池使用情况
        Long currentUsed = commonDataDao.getValue(
            actId,
            this.getComponentId(),
            cmptUseInx,
            attr.getGiftPoolDataKey()
        );
        
        if (currentUsed == null) {
            currentUsed = 0L;
        }
        
        // 2. 计算剩余余额
        Long totalLimit = attr.getGiftPoolTotalLimitLi();
        Long remainingBalance = Math.max(0, totalLimit - currentUsed);
        
        // 3. 返回奖池余额信息
        Map<String, Object> result = new HashMap<>();
        result.put("balance", remainingBalance); // 剩余余额（厘）
        result.put("balanceYuan", remainingBalance / 100.0); // 剩余余额（元）
        
        log.info("queryGiftPoolBalance success, totalLimit:{}, currentUsed:{}, balance:{}", 
                totalLimit, currentUsed, remainingBalance);
        return Response.success(result);
        
    } catch (Exception e) {
        log.error("查询全服礼物奖池余额失败, actId:{}, cmptUseInx:{}", actId, cmptUseInx, e);
        return Response.fail(500, "查询失败");
    }
}
```

## 接口规范

### 请求参数
- `actId`: 活动ID（必填）
- `cmptUseInx`: 组件索引（必填）

### 返回结果
- `balance`: 奖池余额（厘为单位）
- `balanceYuan`: 奖池余额（元为单位）

### 响应示例

```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "balance": 2704000,
    "balanceYuan": 27040.0
  }
}
```

## 实现特点

### 1. 严格遵循注释要求
- 完全按照注释中的3个步骤实现
- 没有添加额外的复杂功能
- 保持简洁明确的逻辑

### 2. 数据安全性
- 使用long类型处理金额（厘为单位）
- 避免浮点数精度问题
- 确保余额不为负数

### 3. 错误处理
- 组件配置不存在时返回404
- 数据库异常时返回500
- 详细的日志记录

### 4. 性能考虑
- 直接查询，无复杂计算
- 最小化数据库访问
- 快速响应

## 依赖关系

### 必需的组件属性
- `giftPoolTotalLimitLi`: 奖池总限额（厘）
- `giftPoolDataKey`: 奖池数据键名

### 必需的服务依赖
- `commonDataDao`: 数据查询服务
- `this.getComponentAttr()`: 组件属性获取
- `this.getComponentId()`: 组件ID获取

## 使用场景

1. **前端查询**: 实时显示奖池余额
2. **运营监控**: 监控奖池使用情况
3. **系统集成**: 其他系统查询余额状态
4. **数据统计**: 作为统计分析的数据源

## 测试验证

### 正常情况测试
```bash
curl "http://localhost:8080/5159/queryGiftPoolBalance?actId=12345&cmptUseInx=1"
```

### 异常情况测试
```bash
# 组件不存在
curl "http://localhost:8080/5159/queryGiftPoolBalance?actId=99999&cmptUseInx=1"

# 参数缺失
curl "http://localhost:8080/5159/queryGiftPoolBalance?actId=12345"
```

## 总结

该实现严格按照方法注释中的要求，实现了：
1. ✅ 使用CommonDataDao查询当前奖池使用情况
2. ✅ 计算剩余余额
3. ✅ 返回奖池余额信息

没有添加任何超出注释要求的功能，保持了实现的简洁性和准确性。接口功能完整，满足查询全服礼物奖池余额的基本需求。
