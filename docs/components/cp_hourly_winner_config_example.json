{"componentId": 5159, "componentName": "CP小时第一名玩法组件", "description": "记录每小时CP榜单第一名并进行奖励发放、应援口令发送和全服广播", "configExample": {"cpRankId": 12345, "cpPhaseId": 67890, "awardBusiId": 810, "scoreRangeAwards": {"1000": {"5000": {"tAwardTskId": 100, "tAwardPkgId": 200, "num": 1, "awardName": "小额奖励", "unit": "厘", "awardIcon": "https://example.com/icon1.png", "awardAmount": 5000, "remark": "1000-5000分奖励"}}, "5001": {"10000": {"tAwardTskId": 101, "tAwardPkgId": 201, "num": 1, "awardName": "中等奖励", "unit": "厘", "awardIcon": "https://example.com/icon2.png", "awardAmount": 10000, "remark": "5001-10000分奖励"}}, "10001": {"0": {"tAwardTskId": 102, "tAwardPkgId": 202, "num": 1, "awardName": "大额奖励", "unit": "厘", "awardIcon": "https://example.com/icon3.png", "awardAmount": 20000, "remark": "10001分以上奖励（无上限）"}}}, "giftPoolTotalLimit": 4000000, "poolInsufficientAward": {"tAwardTskId": 999, "tAwardPkgId": 888, "num": 1, "awardName": "夏日飞骏进场秀", "unit": "天", "awardIcon": "https://example.com/entrance_show.png", "awardAmount": 0, "remark": "奖池不足时发放的替代礼物", "extJson": "{\"duration\": 3}"}, "watchwordLotteryComponentIndex": 1, "watchwordExpireSeconds": 3600, "latestCpComponentIndex": 2, "broadcastBannerId": 5159001, "broadcastTemplateType": 810, "broadcastType": 5, "enableBroadcast": 1, "enableWatchword": 1, "giftPoolLimitKey": "gift_pool_limit"}, "configFields": [{"fieldName": "cpRankId", "labelText": "CP榜单ID", "type": "<PERSON>", "required": true, "description": "指定要监听的CP榜单ID"}, {"fieldName": "cpPhaseId", "labelText": "CP阶段ID", "type": "<PERSON>", "required": true, "description": "指定要监听的CP阶段ID"}, {"fieldName": "awardBusiId", "labelText": "发奖业务ID", "type": "<PERSON>", "required": true, "description": "发奖时使用的业务ID，从下拉列表选择"}, {"fieldName": "scoreRangeAwards", "labelText": "分数区间奖励配置", "type": "Map<Long, Map<Long, AwardAttrConfig>>", "required": true, "description": "根据CP分数区间配置不同奖励，支持多个分数区间"}, {"fieldName": "giftPoolTotalLimit", "labelText": "全服礼物奖池总限额", "type": "<PERSON>", "required": false, "defaultValue": 4000000, "description": "单位：厘，默认4000000厘(40000元)"}, {"fieldName": "poolInsufficientAward", "labelText": "奖池不足时发放的礼物配置", "type": "AwardAttrConfig", "required": false, "description": "当奖池余额不足时，发放的替代礼物配置"}, {"fieldName": "watchwordLotteryComponentIndex", "labelText": "应援口令抽奖组件索引", "type": "<PERSON>", "required": false, "description": "用于发送应援口令的组件索引"}, {"fieldName": "watchwordExpireSeconds", "labelText": "应援口令过期时间", "type": "<PERSON>", "required": false, "defaultValue": 3600, "description": "单位：秒，默认3600秒(1小时)"}, {"fieldName": "latestCpComponentIndex", "labelText": "最新CP组件索引", "type": "<PERSON>", "required": false, "description": "用于获取CP所在房间信息的组件索引"}, {"fieldName": "broadcastBannerId", "labelText": "广播横幅ID", "type": "<PERSON>", "required": false, "description": "全服广播时使用的横幅ID"}, {"fieldName": "broadcastTemplateType", "labelText": "广播模板类型", "type": "Integer", "required": false, "defaultValue": 810, "description": "2==宝贝 3==交友 5==语音房(技能卡)"}, {"fieldName": "broadcastType", "labelText": "广播类型", "type": "Integer", "required": false, "defaultValue": 5, "description": "4=单业务广播；5=多业务广播"}, {"fieldName": "enableBroadcast", "labelText": "是否启用广播", "type": "Integer", "required": false, "defaultValue": 1, "description": "1-启用，0-禁用"}, {"fieldName": "enableWatchword", "labelText": "是否启用应援口令", "type": "Integer", "required": false, "defaultValue": 1, "description": "1-启用，0-禁用"}, {"fieldName": "giftPoolLimitKey", "labelText": "奖池限额控制key", "type": "String", "required": false, "defaultValue": "gift_pool_limit", "description": "用于控制全服礼物奖池限额的key"}], "apiEndpoints": [{"path": "/5159/queryHistoryWinners", "method": "GET", "description": "查询历史小时冠军CP", "parameters": [{"name": "actId", "type": "<PERSON>", "required": true, "description": "活动ID"}, {"name": "cmptUseInx", "type": "<PERSON>", "required": true, "description": "组件使用索引"}, {"name": "date", "type": "String", "required": true, "description": "日期，格式：yyyyMMdd"}], "response": {"type": "Response<List<HistoryWinnerVo>>", "description": "返回历史冠军CP列表，包含用户昵称、头像、主播昵称、头像等信息"}}, {"path": "/5159/queryGiftPoolBalance", "method": "GET", "description": "查询全服礼物奖池余额", "parameters": [{"name": "actId", "type": "<PERSON>", "required": true, "description": "活动ID"}, {"name": "cmptUseInx", "type": "<PERSON>", "required": true, "description": "组件使用索引"}], "response": {"type": "Response<GiftPoolBalanceVo>", "description": "返回奖池余额信息，包含总限额、已使用金额、剩余金额"}}], "events": [{"eventType": "RankingTimeEnd", "description": "榜单结束事件", "handler": "onRankingTimeEnd", "canRetry": true, "processing": ["检查是否为当前组件负责的榜单和阶段", "确保事件处理的幂等性", "查询榜单第一名CP", "记录CP小时第一名", "根据分数区间发放奖励", "控制奖池限额", "发送应援口令到房间", "全服广播第一名CP信息"]}], "dependencies": ["ChannelWatchwordLotteryComponent", "LatestCpComponent", "CommonDataDao", "CommonBroadCastService", "UserInfoService", "HdztRankingThriftClient", "HdztAwardServiceClient"]}