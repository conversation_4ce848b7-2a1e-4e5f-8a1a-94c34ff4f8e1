# CP小时第一名玩法组件实现说明

## 已实现的功能

### 1. processHourlyRankSettle 方法

已完整实现榜单结算的主要逻辑：

#### 核心流程
1. **时间处理**: 使用`TimeKeyHelper.getTimeCode`计算小时编码
2. **榜单查询**: 调用`hdztRankingThriftClient.queryRanking`获取第一名
3. **CP成员解析**: 使用`Const.splitCpMember`解析CP成员信息
4. **用户信息获取**: 调用`userInfoService.getUserInfoWithNickExt`获取用户和主播信息，包括多昵称
5. **房间信息获取**: 通过`latestCpComponent.getLatestCp`获取CP所在房间
6. **数据库记录**: 保存完整的CP小时第一名记录
7. **奖励发放**: 根据分数区间配置发放奖励
8. **奖池管理**: 使用`CommonDataDao.valueIncrIgnoreWithLimit`控制礼物奖池
9. **应援口令**: 发送应援口令到第一名CP所在房间
10. **广播信息**: 广播第一名CP信息

#### 关键特性
- **幂等性保证**: 使用event.getSeq()确保同一事件不重复处理
- **异常处理**: 完整的try-catch机制，确保异常不影响重试
- **数据完整性**: 保存用户头像、昵称、多昵称等完整信息
- **奖池控制**: 实现礼物奖池限额控制和替代奖励机制

### 2. 辅助方法实现

#### checkAndReduceGiftPool
- 使用`CommonDataDao.valueIncrIgnoreWithLimit`实现奖池限额控制
- 支持幂等性检查
- 返回奖池是否充足的状态

#### giveAwardToWinner
- 调用`hdztAwardServiceClient.giveAward`发放奖励
- 支持奖池不足时的替代奖励
- 完整的异常处理机制

#### sendWatchwordToRoom
- 获取第一名CP所在房间信息
- 为后续调用`ChannelWatchwordLotteryComponent`预留接口
- 记录应援口令发送状态

#### broadcastWinnerInfo
- 构建完整的广播数据，包括用户、主播、奖励信息
- 支持多昵称信息的广播
- 支持不同广播类型（单业务、多业务）

## 技术实现亮点

### 1. 数据类型安全
- 所有金额字段使用long类型，单位为厘，避免浮点数精度问题
- 使用`attr.getGiftPoolTotalLimitLi()`获取厘为单位的限额

### 2. 多昵称支持
- 使用`userInfoService.getUserInfoWithNickExt`获取多平台昵称
- 将多昵称信息序列化为JSON存储到数据库
- 在广播时包含多昵称信息

### 3. 幂等性设计
- 事件级别幂等性：使用event.getSeq()防止重复处理
- 奖池操作幂等性：使用CommonDataDao的幂等性方法
- 数据库唯一约束：表设计包含seq字段的唯一索引

### 4. 容错机制
- 榜单数据为空时的处理
- 用户信息获取失败的处理
- 奖励发放失败不影响整体流程
- 广播失败不影响核心业务

## 依赖服务

### 已集成的服务
1. `HdztRankingThriftClient` - 榜单查询
2. `UserInfoService` - 用户信息获取
3. `HdztAwardServiceClient` - 奖励发放
4. `CommonDataDao` - 数据操作和奖池管理
5. `CommonBroadCastService` - 广播服务
6. `LatestCpComponent` - 最新CP信息
7. `ChannelWatchwordLotteryComponent` - 应援口令抽奖

### 配置要求
- `cpRankId`: CP榜单ID
- `phaseId`: 阶段ID
- `scoreRangeAwards`: 分数区间奖励配置
- `giftPoolTotalLimit`: 礼物奖池总限额
- `poolInsufficientAward`: 奖池不足时的替代奖励
- `latestCpComponentIndex`: 最新CP组件索引
- `watchwordLotteryComponentIndex`: 口令抽奖组件索引

## 待完善的功能

### 1. 应援口令具体实现
当前`sendWatchwordToRoom`方法提供了框架，需要根据`ChannelWatchwordLotteryComponent`的具体API完善：
```java
// 需要调用具体的API，例如：
channelWatchwordLotteryComponent.addWatchwordLottery(
    attr.getActId(),
    attr.getWatchwordLotteryComponentIndex(),
    latestCp.getSid(),
    latestCp.getSsid(),
    attr.getWatchwordContent(),
    attr.getWatchwordExpireSeconds()
);
```

### 2. 数据库ID获取优化
当前实现中，保存记录后无法获取自增ID，导致状态更新被跳过。建议：
- 修改Mapper的insert方法支持返回自增ID
- 或者在保存后重新查询记录获取ID

### 3. 广播模板优化
当前广播使用通用的`commonNoticeUnicast`，可以根据业务需求：
- 设计专门的CP小时第一名广播模板
- 支持更丰富的广播内容和样式

## 测试建议

### 1. 单元测试
- 测试各种边界情况（空榜单、分数为0等）
- 测试奖池充足和不足的情况
- 测试用户信息获取失败的情况

### 2. 集成测试
- 测试完整的榜单结算流程
- 验证数据库记录的完整性
- 验证奖励发放的正确性

### 3. 性能测试
- 测试高并发情况下的幂等性
- 测试大量用户信息获取的性能

## 部署注意事项

1. **数据库表创建**: 确保执行了建表SQL
2. **组件配置**: 正确配置所有必需的属性
3. **依赖组件**: 确保相关组件（LatestCpComponent、ChannelWatchwordLotteryComponent）已正确部署
4. **权限配置**: 确保有奖励发放的相关权限
5. **监控告警**: 建议对关键流程添加监控和告警
