# CP小时第一名玩法组件

## 组件概述

CP小时第一名玩法组件(ComponentId: 5159)用于实现每小时CP榜单第一名的记录、奖励发放和广播功能。

## 功能特性

### 1. 榜单结束事件结算
- 监听`RankingTimeEnd`事件，每小时结束时自动处理
- 记录CP榜单第一名信息
- 根据分数区间发放不同奖励
- 支持礼物奖池限额控制（使用long类型避免浮点数精度问题）
- 奖池不足时自动切换替代奖励（夏日飞骏进场秀3天）

### 2. 应援口令抽奖
- 对第一名CP所在房间发送应援口令"夏日派对，浪漫加倍"
- 集成`ChannelWatchwordLotteryComponent`组件实现抽奖功能
- 支持配置口令过期时间

### 3. 全服广播
- 广播第一名CP信息，包括用户头像、昵称、主播头像、昵称
- 支持多种广播模板和业务ID配置

### 4. HTTP接口
- 查询历史小时冠军CP接口
- 查询全服礼物奖池余额接口

## 数据库设计

### 表结构：cmpt_5159_cp_hourly_winner

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint(20) | 主键ID |
| act_id | bigint(20) | 活动ID |
| cmpt_use_inx | bigint(20) | 组件使用索引 |
| seq | varchar(128) | 事件序列号，用于幂等性控制 |
| rank_id | bigint(20) | 榜单ID |
| phase_id | bigint(20) | 阶段ID |
| hour_code | varchar(32) | 小时编码，格式：yyyyMMddHH |
| user_uid | bigint(20) | 用户UID |
| anchor_uid | bigint(20) | 主播UID |
| cp_score | bigint(20) | CP分数 |
| user_nick | varchar(128) | 用户昵称 |
| user_avatar | varchar(512) | 用户头像 |
| anchor_nick | varchar(128) | 主播昵称 |
| anchor_avatar | varchar(512) | 主播头像 |
| user_multi_nick | text | 用户多昵称信息JSON |
| anchor_multi_nick | text | 主播多昵称信息JSON |
| award_amount | bigint(20) | 奖励金额，单位：厘 |
| award_config | text | 奖励配置JSON |
| gift_pool_used | bigint(20) | 使用的礼物奖池金额，单位：厘 |
| is_pool_sufficient | tinyint(1) | 奖池是否充足：1-充足，0-不足 |
| channel_sid | bigint(20) | 房间SID |
| channel_ssid | bigint(20) | 房间SSID |
| watchword_sent | tinyint(1) | 是否已发送应援口令：1-已发送，0-未发送 |
| broadcast_sent | tinyint(1) | 是否已广播：1-已广播，0-未广播 |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |

## 组件配置

### 主要配置项

- **业务ID**: 默认200（游戏生态）
- **CP榜单ID**: 需要监听的CP榜单ID
- **阶段ID**: 榜单阶段ID
- **分数区间奖励配置**: 根据不同分数区间配置不同奖励
- **全服礼物奖池总限额**: 默认4000000厘（40000元）
- **奖池不足时的替代奖励**: 夏日飞骏进场秀3天
- **应援口令内容**: 默认"夏日派对，浪漫加倍"
- **应援口令过期时间**: 默认300秒
- **广播业务类型**: 默认4（单业务广播）

## HTTP接口

### 1. 查询历史小时冠军CP

**接口地址**: `GET /5159/queryHistoryWinners`

**请求参数**:
- `actId`: 活动ID
- `cmptUseInx`: 组件索引
- `dateCode`: 日期编码（支持前缀匹配，如：20250721）

**返回结果**:
```json
{
  "code": 0,
  "msg": "success",
  "data": [
    {
      "userNick": "用户昵称",
      "userAvatar": "用户头像URL",
      "anchorNick": "主播昵称", 
      "anchorAvatar": "主播头像URL",
      "hourTime": "2025072114",
      "cpScore": 12345
    }
  ]
}
```

### 2. 查询全服礼物奖池余额

**接口地址**: `GET /5159/queryGiftPoolBalance`

**请求参数**:
- `actId`: 活动ID
- `cmptInx`: 组件索引

**返回结果**:
```json
{
  "code": 0,
  "msg": "success", 
  "data": {
    "totalLimit": 40000.0,
    "currentBalance": 12960.0,
    "usedAmount": 27040.0
  }
}
```

## 部署步骤

1. **执行数据库建表SQL**:
   ```sql
   -- 执行 docs/sql/cmpt_5159_cp_hourly_winner.sql
   ```

2. **在活动配置中添加组件**:
   - ComponentId设置为`5159`
   - 配置组件属性，包括榜单ID、奖励配置等

3. **确保依赖组件已配置**:
   - `ChannelWatchwordLotteryComponent`组件已正确配置

## 配置示例

```json
{
  "busiId": 200,
  "cpRankId": 12345,
  "phaseId": 67890,
  "scoreRangeAwards": {
    "1000": {
      "tAwardTskId": 100001,
      "tAwardPkgId": 200001,
      "awardAmount": 500000,
      "awardName": "豪华礼包"
    },
    "5000": {
      "tAwardTskId": 100002,
      "tAwardPkgId": 200002,
      "awardAmount": 1000000,
      "awardName": "超级豪华礼包"
    }
  },
  "giftPoolTotalLimit": 4000000,
  "poolInsufficientAward": {
    "tAwardTskId": 100003,
    "tAwardPkgId": 200003,
    "awardName": "夏日飞骏进场秀3天"
  },
  "watchwordContent": "夏日派对，浪漫加倍",
  "watchwordExpireSeconds": 300,
  "broadcastBusiType": 4
}
```

## 注意事项

1. **幂等性保证**: 使用seq字段确保同一事件不会重复处理
2. **数据类型**: 涉及金额的字段使用long类型，以厘为单位避免浮点数精度问题
3. **奖池管理**: 需要实时更新奖池余额，确保数据一致性
4. **错误处理**: 组件支持重试机制，需要确保业务逻辑的幂等性
5. **依赖组件**: 确保ChannelWatchwordLotteryComponent组件正常工作

## TODO项

当前实现中以下功能标记为TODO，需要后续完善：

1. `processHourlyRankSettle` - 榜单结算主逻辑
2. `queryHistoryWinners` - 历史冠军查询接口实现
3. `queryGiftPoolBalance` - 奖池余额查询接口实现
4. `giveAwardToWinner` - 奖励发放逻辑
5. `checkAndReduceGiftPool` - 奖池管理逻辑
6. `sendWatchwordToRoom` - 应援口令发送逻辑
7. `broadcastWinnerInfo` - 广播逻辑

这些功能的框架已搭建完成，可以分步骤实现具体业务逻辑。
