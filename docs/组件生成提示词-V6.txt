实现这个组件
https://ku.baidu-int.com/knowledge/HFVrC7hq1Q/SB5xNxJC_l/4xX96ppH_s/hxt1wNz2QRyrfo


------------------------------------------------------------------------------
------------------------------->第1轮对话<-------------------------------------
------------------------------------------------------------------------------
组件开发分为以下3个步骤：
1、数据库设计

2、组件配置设计规划

3、方法分解、实现

根据component_ai.md中的规范，创建cp小时第一名玩法组件的代码文件，实现以下功能：
------------------------------------------------------------------------------

数据库设计：
1. 设计1个mysql表，用于存储每小时cp榜单的第一名，表中应该包含seq字段，用于约束接口的幂等性，表结构设计如下：
-- CP小时第一名玩法组件数据库表
-- 组件ID: 5159

CREATE TABLE `cmpt_5159_cp_hourly_winner` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `act_id` bigint(20) NOT NULL COMMENT '活动ID',
  `cmpt_use_inx` bigint(20) NOT NULL COMMENT '组件使用索引',
  `seq` varchar(128) NOT NULL COMMENT '事件序列号，用于幂等性控制',
  `rank_id` bigint(20) NOT NULL COMMENT '榜单ID',
  `phase_id` bigint(20) NOT NULL COMMENT '阶段ID',
  `hour_code` varchar(32) NOT NULL COMMENT '小时编码，格式：yyyyMMddHH',
  `user_uid` bigint(20) NOT NULL COMMENT '用户UID',
  `anchor_uid` bigint(20) NOT NULL COMMENT '主播UID',
  `cp_score` bigint(20) NOT NULL COMMENT 'CP分数',
  `award_amount` bigint(20) DEFAULT 0 COMMENT '奖励金额，单位：厘',
  `is_pool_sufficient` tinyint(1) DEFAULT 1 COMMENT '奖池是否充足：1-充足，0-不足',
  `channel_sid` bigint(20) DEFAULT NULL COMMENT '房间SID',
  `channel_ssid` bigint(20) DEFAULT NULL COMMENT '房间SSID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_act_cmpt_seq` (`act_id`, `cmpt_use_inx`, `seq`),
  KEY `idx_act_cmpt_hour` (`act_id`, `cmpt_use_inx`, `hour_code`),
  KEY `idx_act_cmpt_rank_phase` (`act_id`, `cmpt_use_inx`, `rank_id`, `phase_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='CP小时第一名玩法组件数据表';

------------------------------------------------------------------------------
程序实现：

榜单结束事件结算方法：
1. 每小时结束时，记录CP榜单第一名。
2. 对第一名CP发放奖励，根据第一分数区间不同，发放不一样的奖励，发奖奖励可配置在组件属性类。
3. 全服礼物奖池总限量40000.0元（当前礼物剩余12960元），若CP获得礼物奖励时，剩余礼物不足，则将以夏日飞骏进场秀3天形式进行发放。奖池限额以及奖池不足时发放的礼物配置，放到组件属性配置。奖品发放时限额的累计和控制可以用com.yy.gameecology.activity.dao.mysql.CommonDataDao.valueIncrIgnoreWithLimit方法
3. 对第一名CP所在的房间，发送应援口令“夏日派对，浪漫加倍”，可参与抽奖，有机会获得进场秀、头像框等奖励，这个功能的实现，调用ChannelWatchwordLotteryComponent组件实现。第一名cp所在房间的数据可以用LatestCpComponent组件中的getLatestCp方法获取。
4. 每小时结束时全服广播第一名CP信息，广播信息包括用户头像、用户昵称、主播头像、主播昵称。用户信息需要把多昵称信息页放到扩展字段一起广播出去。


http接口：
接口1. 提供查询历史小时冠军CP接口，传入参数为，活动id、组件索引、日期，返回结果为用户昵称、用户头像、主播昵称、主播头像
接口2. 查询全服礼物奖池余额接口，传入参数为，活动id、组件索引，返回结果为奖池余额

注意事项：
数据库操作，请遵守component_ai.md中的【代码数据操作要求】

实现的时候，只要定义好数据结构、方法定义，对于【榜单结束事件结算方法、http接口1、接口2】的方法主逻辑可以先不实现，可明确注明TODO，以便后面分步骤实现

------------------------------------------------------------------------------
------------------------------->第2轮对话<-------------------------------------
------------------------------------------------------------------------------
完善一下 private void processHourlyRankSettle(RankingTimeEnd event, CpHourlyWinnerComponentAttr attr)的整体逻辑，包括子方法

