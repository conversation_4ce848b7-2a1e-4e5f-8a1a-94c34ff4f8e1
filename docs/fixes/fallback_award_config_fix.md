# 替代奖励配置修复说明

## 问题描述

在原始的 `CpHourlyWinnerComponentAttr` 实现中，`fallbackAward` 字段被定义为单个 `AwardAttrConfig` 类型：

```java
// 错误的定义方式
@ComponentAttrField(labelText = "奖池不足时的替代奖励")
private AwardAttrConfig fallbackAward;
```

根据组件属性编码规范，**不支持单个非基础类型的数据类型**，这种配置会导致组件属性解析失败。

## 解决方案

按照规范要求，将单个 `AwardAttrConfig` 改为 `Map<String, AwardAttrConfig>` 格式：

```java
// 正确的定义方式
@ComponentAttrField(labelText = "奖池不足时的替代奖励", 
        remark = "当礼物奖池余额不足时发放的奖励，使用Map格式：key为奖励类型，value为奖励配置",
        subFields = {
                @SubField(fieldName = Constant.KEY1, type = String.class, labelText = "奖励类型", remark = "如：fallback、default等"),
                @SubField(fieldName = Constant.VALUE, type = AwardAttrConfig.class, labelText = "奖励配置")
        })
private Map<String, AwardAttrConfig> fallbackAwards = Maps.newLinkedHashMap();
```

## 修改内容

### 1. 字段定义修改

**修改前**：
```java
private AwardAttrConfig fallbackAward;
```

**修改后**：
```java
private Map<String, AwardAttrConfig> fallbackAwards = Maps.newLinkedHashMap();
```

### 2. 添加便捷方法

为了保持向后兼容性和使用便利性，添加了以下方法：

```java
/**
 * 获取替代奖励配置
 * 优先级：fallback > default > 第一个配置的奖励
 */
public AwardAttrConfig getFallbackAward() {
    // 优先获取"fallback"类型的奖励
    AwardAttrConfig fallbackAward = fallbackAwards.get("fallback");
    if (fallbackAward != null) {
        return fallbackAward;
    }
    
    // 如果没有"fallback"类型，获取"default"类型
    fallbackAward = fallbackAwards.get("default");
    if (fallbackAward != null) {
        return fallbackAward;
    }
    
    // 如果都没有，返回第一个配置的奖励
    if (!fallbackAwards.isEmpty()) {
        return fallbackAwards.values().iterator().next();
    }
    
    return null;
}

/**
 * 设置替代奖励配置（兼容性方法）
 */
public void setFallbackAward(AwardAttrConfig awardConfig) {
    if (awardConfig != null) {
        fallbackAwards.put("fallback", awardConfig);
    }
}
```

### 3. 配置格式变更

**修改前**：
```json
{
  "fallbackAward": {
    "tAwardTskId": 100003,
    "tAwardPkgId": 200001,
    "num": 1,
    "awardName": "夏日飞骏进场秀3天"
  }
}
```

**修改后**：
```json
{
  "fallbackAwards": {
    "fallback": {
      "tAwardTskId": 100003,
      "tAwardPkgId": 200001,
      "num": 1,
      "awardName": "夏日飞骏进场秀3天"
    },
    "default": {
      "tAwardTskId": 100004,
      "tAwardPkgId": 200002,
      "num": 1,
      "awardName": "默认进场秀3天"
    }
  }
}
```

## 优势

### 1. 符合规范
- 严格遵循组件属性编码规范
- 避免组件属性解析失败

### 2. 更灵活的配置
- 支持多种类型的替代奖励
- 可以配置不同场景下的奖励

### 3. 向后兼容
- 提供 `getFallbackAward()` 方法保持API兼容性
- 提供 `setFallbackAward()` 方法支持旧的设置方式

### 4. 优先级机制
- `fallback` 类型：最高优先级
- `default` 类型：次优先级
- 其他类型：按配置顺序

## 使用示例

### 基本使用
```java
// 获取替代奖励配置
AwardAttrConfig award = attr.getFallbackAward();

// 设置替代奖励配置
AwardAttrConfig newAward = new AwardAttrConfig();
newAward.setAwardName("新的替代奖励");
attr.setFallbackAward(newAward);
```

### 高级配置
```java
// 配置多种类型的替代奖励
Map<String, AwardAttrConfig> fallbackAwards = attr.getFallbackAwards();

// 设置fallback类型奖励（最高优先级）
AwardAttrConfig fallbackAward = new AwardAttrConfig();
fallbackAward.setAwardName("fallback奖励");
fallbackAwards.put("fallback", fallbackAward);

// 设置default类型奖励（次优先级）
AwardAttrConfig defaultAward = new AwardAttrConfig();
defaultAward.setAwardName("default奖励");
fallbackAwards.put("default", defaultAward);

// 设置特殊场景奖励
AwardAttrConfig specialAward = new AwardAttrConfig();
specialAward.setAwardName("特殊场景奖励");
fallbackAwards.put("special", specialAward);
```

## 测试覆盖

添加了完整的单元测试来验证新的配置方式：

1. **空配置测试** - 验证空配置时的行为
2. **优先级测试** - 验证不同类型奖励的优先级
3. **兼容性测试** - 验证向后兼容的API
4. **多配置测试** - 验证多种奖励类型的配置

## 迁移指南

### 对于新项目
直接使用新的 `Map<String, AwardAttrConfig>` 格式进行配置。

### 对于现有项目
1. 更新组件属性配置格式
2. 将原有的单个 `fallbackAward` 配置迁移到 `fallbackAwards.fallback`
3. 代码中继续使用 `getFallbackAward()` 方法，无需修改业务逻辑

## 注意事项

1. **配置格式** - 必须使用Map格式，key为字符串，value为AwardAttrConfig
2. **优先级** - 建议使用"fallback"作为主要的替代奖励类型
3. **兼容性** - 现有的业务代码无需修改，继续使用 `getFallbackAward()` 方法
4. **扩展性** - 可以根据需要添加更多类型的替代奖励

## 相关文件

- `CpHourlyWinnerComponentAttr.java` - 主要修改文件
- `CpHourlyWinnerComponentTest.java` - 测试文件
- `cp_hourly_winner_component.md` - 组件文档
- `cp_hourly_winner_implementation.md` - 实现文档
