# CP小时第一名玩法组件实现详解

## processHourlyRankSettle 方法实现

### 方法概述
`processHourlyRankSettle` 是CP小时第一名玩法组件的核心方法，负责处理每小时榜单结束时的所有业务逻辑。

### 实现流程

#### 1. 获取榜单第一名信息
```java
private Rank getFirstRankFromEvent(RankingTimeEnd event, CpHourlyWinnerComponentAttr attr)
```
- 根据事件结束时间计算小时时间码（yyyyMMddHH格式）
- 调用 `hdztRankingThriftClient.queryRanking()` 查询榜单第一名
- 验证榜单数据有效性（非空、分数大于0）

#### 2. 解析CP成员信息
```java
CpUid cpUid = Const.splitCpMember(firstRank.getMember());
```
- 使用 `Const.splitCpMember()` 解析CP成员字符串
- 提取用户UID和主播UID
- CP成员格式：`userUid|anchorUid`

#### 3. 获取用户信息
```java
UserInfoWithNickExt userInfo = userInfoService.getUserInfoWithNickExt(cpUid.getUserUid());
UserInfoWithNickExt anchorInfo = userInfoService.getUserInfoWithNickExt(cpUid.getAnchorUid());
```
- 获取用户和主播的详细信息（昵称、头像等）
- 用于后续的数据库记录和广播

#### 4. 创建数据库记录
```java
private Cmpt5159CpHourlyWinner createHourlyWinnerRecord(...)
```
- 创建小时冠军记录对象
- 设置基本信息：活动ID、组件索引、序列号、小时编码
- 设置CP信息：用户UID、主播UID、昵称、头像、分数
- 设置房间信息：通过 `getAnchorChannelInfo()` 获取主播所在房间的SID、SSID

#### 5. 根据分数区间发放奖励
```java
private void processAwardByScore(...)
```

##### 5.1 获取奖励配置
- 根据第一名分数调用 `attr.getAwardByScore(score)` 获取对应奖励配置
- 支持多个分数区间，每个区间对应不同奖励

##### 5.2 检查礼物奖池余额
```java
boolean isGiftPoolSufficient = attr.isGiftPoolSufficient(awardAmount);
```
- 检查当前奖池余额是否足够发放奖励
- 奖池总限额：40000元，当前余额：12960元

##### 5.3 发放奖励
**正常奖励发放** (`sendNormalAward`)：
- 奖池余额充足时执行
- 构建任务包信息：`Map<Long, Map<Long, Integer>> taskPackageIds`
- 分别给用户和主播发放奖励
- 使用 `hdztAwardServiceClient.doBatchWelfare()` 发放
- 扣减奖池余额
- 记录奖励类型为1（礼物奖励）

**替代奖励发放** (`sendFallbackAward`)：
- 奖池余额不足时执行
- 发放配置的替代奖励（如：夏日飞骏进场秀3天）
- 不扣减奖池余额
- 记录奖励类型为2（进场秀奖励）

#### 6. 发送应援口令到房间
```java
private void sendSupportSloganToRoom(...)
```
- 获取配置的应援口令文案（默认："夏日派对，浪漫加倍"）
- 获取主播所在房间信息（SID/SSID）
- 生成唯一的应援口令序列号
- 设置应援口令过期时间（默认30分钟）
- 调用 `ChannelWatchwordLotteryComponent.addWatchwordLotteryBox()` 创建应援口令抽奖

**房间信息获取逻辑**：
1. 优先从 `OnlineChannelService.getChannelInfoVo()` 获取在线频道信息
2. 如果在线频道服务无数据，从 `CommonService.getNoCacheUserCurrentChannel()` 获取用户当前频道
3. 支持重试机制，确保获取到有效的房间信息

#### 7. 广播第一名CP信息
```java
private void broadcastWinnerInfo(...)
```
- 检查是否启用广播功能
- 构建广播数据：用户昵称、头像、主播昵称、头像、分数、小时时间
- 根据配置的广播模板类型选择模板（宝贝/交友/语音房）
- 使用 `commonBroadCastService.commonBannerBroadcast()` 发送横幅广播

#### 8. 保存记录到数据库
```java
cpHourlyWinnerComponentDao.insertHourlyWinner(record);
```
- 将完整的冠军记录保存到数据库
- 包含所有处理结果：奖励信息、奖池余额、奖励类型等

### 关键技术点

#### 1. 幂等性保证
- 使用事件序列号 `event.getSeq()` 作为唯一标识
- 数据库表设计包含seq字段的唯一约束
- 发奖时使用MD5加密的序列号确保唯一性

#### 2. 错误处理
- 每个步骤都有独立的异常处理
- 关键步骤失败会抛出异常触发重试
- 非关键步骤（如广播）失败不影响主流程

#### 3. 数据一致性
- 奖池余额的检查和扣减在同一事务中
- 数据库记录包含完整的处理状态信息
- 支持事件重试机制

#### 4. 性能考虑
- 用户信息查询使用缓存
- 榜单查询限制返回数量为1
- 异步处理非关键业务（广播、应援口令）

### 配置示例

```json
{
  "cpRankId": 123456,
  "scoreRangeAwards": {
    "1000-5000": {
      "tAwardTskId": 100001,
      "tAwardPkgId": 200001,
      "num": 1,
      "awardName": "小额礼物",
      "awardAmount": 50000
    },
    "5001-10000": {
      "tAwardTskId": 100002,
      "tAwardPkgId": 200002,
      "num": 1,
      "awardName": "大额礼物",
      "awardAmount": 100000
    }
  },
  "giftPoolCurrentBalance": 1296000,
  "fallbackAwards": {
    "fallback": {
      "tAwardTskId": 100003,
      "tAwardPkgId": 200003,
      "num": 1,
      "awardName": "夏日飞骏进场秀3天"
    }
  },
  "supportSlogan": "夏日派对，浪漫加倍",
  "enableBroadcast": true,
  "broadcastBannerId": 5159001,
  "broadcastBannerType": 1
}
```

### 测试验证

#### 单元测试覆盖
- 分数区间匹配逻辑测试
- 礼物奖池余额管理测试
- CP成员解析测试
- 配置验证测试
- 边界条件测试

#### 集成测试建议
1. 模拟榜单结束事件
2. 验证数据库记录创建
3. 验证奖励发放调用
4. 验证广播消息发送
5. 验证幂等性处理

### 监控指标建议
- 处理成功率
- 处理耗时
- 奖励发放成功率
- 奖池余额变化
- 异常发生频率

### 后续优化方向
1. 完善应援口令发送功能
2. 增加房间信息获取
3. 优化用户信息查询性能
4. 增加更详细的业务监控
5. 支持更灵活的奖励配置
