# CP小时第一名玩法组件实现

## 概述

本文档描述了CP小时第一名玩法组件（ComponentId: 5159）的完整实现，该组件用于处理每小时CP榜单第一名的奖励发放、应援口令、广播等功能。

## 实现文件清单

### 1. 数据库相关
- **数据库表模型**: `hdpt_activity_xlogic_common/src/main/java/com/yy/gameecology/common/db/model/gameecology/cmpt/Cmpt5159CpHourlyWinner.java`
- **Mapper接口**: `hdpt_activity_xlogic_common/src/main/java/com/yy/gameecology/common/db/mapper/gameecology/cmpt/Cmpt5159CpHourlyWinnerMapper.java`
- **建表SQL**: `docs/sql/cmpt_5159_cp_hourly_winner.sql`

### 2. 组件核心代码
- **组件主逻辑**: `hdpt_activity_xlogic_base/src/main/java/com/yy/gameecology/hdzj/element/component/CpHourlyWinnerComponent.java`
- **组件属性配置**: `hdpt_activity_xlogic_base/src/main/java/com/yy/gameecology/hdzj/element/component/attr/CpHourlyWinnerComponentAttr.java`
- **数据访问层**: `hdpt_activity_xlogic_base/src/main/java/com/yy/gameecology/hdzj/element/component/dao/CpHourlyWinnerComponentDao.java`

### 3. 组件ID定义
- 在`ComponentId.java`中添加了组件ID: `CP_HOURLY_WINNER = 5159L`

## 数据库设计

### 表结构：cmpt_5159_cp_hourly_winner

```sql
CREATE TABLE `cmpt_5159_cp_hourly_winner` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `act_id` bigint(20) NOT NULL COMMENT '活动ID',
  `cmpt_use_inx` bigint(20) NOT NULL COMMENT '组件使用索引',
  `seq` varchar(128) NOT NULL COMMENT '事件序列号，用于幂等性控制',
  `rank_id` bigint(20) NOT NULL COMMENT '榜单ID',
  `phase_id` bigint(20) NOT NULL COMMENT '阶段ID',
  `hour_code` varchar(32) NOT NULL COMMENT '小时编码，格式：yyyyMMddHH',
  `user_uid` bigint(20) NOT NULL COMMENT '用户UID',
  `anchor_uid` bigint(20) NOT NULL COMMENT '主播UID',
  `cp_score` bigint(20) NOT NULL COMMENT 'CP分数',
  `award_amount` bigint(20) DEFAULT 0 COMMENT '奖励金额，单位：厘',
  `is_pool_sufficient` tinyint(1) DEFAULT 1 COMMENT '奖池是否充足：1-充足，0-不足',
  `channel_sid` bigint(20) DEFAULT NULL COMMENT '房间SID',
  `channel_ssid` bigint(20) DEFAULT NULL COMMENT '房间SSID',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_act_cmpt_seq` (`act_id`, `cmpt_use_inx`, `seq`),
  KEY `idx_act_cmpt_hour` (`act_id`, `cmpt_use_inx`, `hour_code`),
  KEY `idx_act_cmpt_rank_phase` (`act_id`, `cmpt_use_inx`, `rank_id`, `phase_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='CP小时第一名玩法组件数据表';
```

## 组件功能

### 1. 榜单结束事件处理
- 监听`RankingTimeEnd`事件
- 使用`seq`字段确保幂等性
- 记录每小时CP榜单第一名

### 2. 奖励发放
- 根据分数区间配置发放不同奖励
- 管理全服礼物奖池（总限量40000.0元）
- 奖池不足时发放替代奖励（夏日飞骏进场秀3天）

### 3. 应援口令功能
- 调用`ChannelWatchwordLotteryComponent`组件
- 使用`LatestCpComponent.getLatestCp`获取房间信息
- 发送应援口令"夏日派对，浪漫加倍"

### 4. 全服广播
- 广播第一名CP信息（用户头像、昵称、主播头像、昵称）
- 包含多昵称信息

### 5. HTTP接口
- `/5159/queryHistoryWinners` - 查询历史小时冠军CP
- `/5159/queryPoolBalance` - 查询全服礼物奖池余额

## 组件属性配置

### 主要配置项
- `cpRankId`: CP榜单ID
- `cpPhaseId`: CP阶段ID
- `scoreRangeAwards`: 分数区间奖励配置
- `totalGiftPoolLimit`: 全服礼物奖池总限量
- `alternativeAward`: 奖池不足时的替代奖励
- `watchwordText`: 应援口令文本
- `watchwordExpireSeconds`: 应援口令过期时间
- `watchwordLotteryComponentIndex`: ChannelWatchwordLotteryComponent组件索引
- `latestCpComponentIndex`: LatestCpComponent组件索引
- `totalAwardLimit`: 奖励总限额（单位：厘）
- `dailyAwardLimit`: 每日奖励限额（单位：厘）
- `hourlyAwardLimit`: 每小时奖励限额（单位：厘）

## 实现状态

### 已完成
1. ✅ 数据库表设计和模型类
2. ✅ 组件属性配置类
3. ✅ 数据访问层（DAO）
4. ✅ 组件主逻辑框架
5. ✅ 事件监听器定义
6. ✅ HTTP接口定义
7. ✅ 应援口令发送方法（已实现）
8. ✅ `processHourlyRankSettle` - 榜单结算主逻辑（已完整实现）
9. ✅ `broadcastWinnerInfo` - 全服广播逻辑（已完整实现）

### 待实现（标记为TODO）
1. 🔄 `queryHistoryWinners` - 查询历史冠军接口实现
2. 🔄 `queryPoolBalance` - 查询奖池余额接口实现
3. 🔄 抽奖模式的奖励发放逻辑（在distributeAwardToUser和distributeAwardToAnchor方法中）

## 代码规范遵循

1. **幂等性**: 使用`seq`字段和`INSERT IGNORE`语句确保操作幂等性
2. **数据类型**: 涉及金额的字段使用`long`类型避免浮点数精度问题
3. **组件架构**: 遵循现有组件的设计模式和目录结构
4. **错误处理**: 完善的异常处理和日志记录
5. **依赖注入**: 正确使用Spring的依赖注入机制

## 限额控制功能

### 多层级限额控制
组件实现了完善的多层级限额控制机制，使用`CommonDataDao.valueIncrIgnoreWithLimit`方法确保奖励发放的限额管理：

#### 1. 总限额控制
- **配置项**: `totalAwardLimit`
- **作用**: 控制整个活动期间的奖励总金额
- **Key**: `total_award_limit`
- **幂等性**: 使用`{seq}_total`确保同一事件不重复计算

#### 2. 每日限额控制
- **配置项**: `dailyAwardLimit`
- **作用**: 控制每天的奖励发放金额
- **Key**: `daily_award_limit_{yyyyMMdd}`
- **幂等性**: 使用`{seq}_daily`确保同一事件不重复计算

#### 3. 每小时限额控制
- **配置项**: `hourlyAwardLimit`
- **作用**: 控制每小时的奖励发放金额
- **Key**: `hourly_award_limit_{yyyyMMddHH}`
- **幂等性**: 使用`{seq}_hourly`确保同一事件不重复计算

### 限额检查流程
1. **按优先级检查**: 总限额 → 每日限额 → 每小时限额
2. **任一限额超出**: 立即停止发放，记录警告日志
3. **所有限额通过**: 执行实际的奖励发放
4. **幂等性保证**: 相同seq的请求不会重复累计限额

### 限额配置说明
- 设置为`0`表示不限额
- 单位统一为厘（1元 = 1000厘）
- 支持运行时动态调整配置

## 新增实现的方法

### 1. processHourlyRankSettle（主要结算逻辑）
- ✅ `queryFirstRankWinner` - 查询榜单第一名
- ✅ `createWinnerRecord` - 创建获胜者记录
- ✅ `processAwardDistribution` - 处理奖励发放
- ✅ `checkLimitAndDistributeAward` - 检查限额并发放奖励（新增）
- ✅ `distributeAwardToUser` - 发放奖励给用户
- ✅ `distributeAwardToAnchor` - 发放奖励给主播
- ✅ `broadcastWinnerInfo` - 全服广播第一名CP信息

### 2. 核心功能特性
- ✅ 榜单第一名查询和CP成员解析
- ✅ 数据库记录插入（幂等性保证）
- ✅ 分数区间奖励匹配
- ✅ 奖池余额检查和扣减
- ✅ 奖池不足时的替代奖励
- ✅ **多层级限额控制**（总限额、每日限额、每小时限额）
- ✅ **使用CommonDataDao.valueIncrIgnoreWithLimit进行限额累计和控制**
- ✅ 用户和主播信息获取
- ✅ 多昵称信息处理
- ✅ 全服广播数据构建
- ✅ 应援口令房间发送

## 下一步实现建议

1. 完善HTTP接口的具体实现（queryHistoryWinners、queryPoolBalance）
2. 实现抽奖模式的奖励发放逻辑
3. 编写单元测试
4. 进行集成测试
5. 配置奖池初始余额

## 注意事项

1. 确保在实际部署前先在数据库中创建对应的表
2. 配置正确的组件索引以调用其他组件
3. 测试幂等性和异常重试机制
4. 验证奖池余额管理逻辑
