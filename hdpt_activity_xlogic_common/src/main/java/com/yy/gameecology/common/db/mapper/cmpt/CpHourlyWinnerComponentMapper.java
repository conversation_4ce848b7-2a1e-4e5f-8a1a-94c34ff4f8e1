package com.yy.gameecology.common.db.mapper.cmpt;

import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5159CpHourlyWinner;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * CP小时第一名玩法组件Mapper接口
 * 
 * <AUTHOR> Generated
 * @date 2025-07-22
 */
public interface CpHourlyWinnerComponentMapper {
    
    /**
     * 插入CP小时第一名记录
     * 
     * @param record CP小时第一名记录
     * @return 影响行数
     */
    int insert(Cmpt5159CpHourlyWinner record);
    
    /**
     * 检查事件是否已处理（幂等性检查）
     * 
     * @param actId 活动ID
     * @param cmptUseInx 组件使用索引
     * @param seq 事件序列号
     * @return 记录数量
     */
    int countBySeq(@Param("actId") Long actId, 
                   @Param("cmptUseInx") Long cmptUseInx, 
                   @Param("seq") String seq);
    
    /**
     * 根据小时编码查询历史冠军CP
     * 
     * @param actId 活动ID
     * @param cmptUseInx 组件使用索引
     * @param hourCodePrefix 小时编码前缀（支持前缀匹配）
     * @param limit 限制条数
     * @return CP小时第一名记录列表
     */
    List<Cmpt5159CpHourlyWinner> selectByHourCodePrefix(@Param("actId") Long actId,
                                                         @Param("cmptUseInx") Long cmptUseInx,
                                                         @Param("hourCodePrefix") String hourCodePrefix,
                                                         @Param("limit") Integer limit);
    
    /**
     * 查询指定时间范围内的历史冠军CP
     * 
     * @param actId 活动ID
     * @param cmptUseInx 组件使用索引
     * @param startHourCode 开始小时编码
     * @param endHourCode 结束小时编码
     * @param limit 限制条数
     * @return CP小时第一名记录列表
     */
    List<Cmpt5159CpHourlyWinner> selectByHourCodeRange(@Param("actId") Long actId,
                                                        @Param("cmptUseInx") Long cmptUseInx,
                                                        @Param("startHourCode") String startHourCode,
                                                        @Param("endHourCode") String endHourCode,
                                                        @Param("limit") Integer limit);
    
    /**
     * 更新应援口令发送状态
     * 
     * @param id 记录ID
     * @param watchwordSent 是否已发送应援口令
     * @return 影响行数
     */
    int updateWatchwordSent(@Param("id") Long id, 
                           @Param("watchwordSent") Boolean watchwordSent);
    
    /**
     * 更新广播发送状态
     * 
     * @param id 记录ID
     * @param broadcastSent 是否已广播
     * @return 影响行数
     */
    int updateBroadcastSent(@Param("id") Long id, 
                           @Param("broadcastSent") Boolean broadcastSent);
    
    /**
     * 根据ID查询记录
     * 
     * @param id 记录ID
     * @return CP小时第一名记录
     */
    Cmpt5159CpHourlyWinner selectById(@Param("id") Long id);
    
    /**
     * 查询最近的冠军记录
     * 
     * @param actId 活动ID
     * @param cmptUseInx 组件使用索引
     * @param limit 限制条数
     * @return CP小时第一名记录列表
     */
    List<Cmpt5159CpHourlyWinner> selectLatest(@Param("actId") Long actId,
                                              @Param("cmptUseInx") Long cmptUseInx,
                                              @Param("limit") Integer limit);
}
