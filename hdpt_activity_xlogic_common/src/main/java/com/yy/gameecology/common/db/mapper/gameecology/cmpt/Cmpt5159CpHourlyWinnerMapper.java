package com.yy.gameecology.common.db.mapper.gameecology.cmpt;

import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5159CpHourlyWinner;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * CP小时第一名玩法组件Mapper
 * 
 * <AUTHOR> Generated
 * @date 2025-07-23
 */
public interface Cmpt5159CpHourlyWinnerMapper {

    /**
     * 插入记录（幂等性）
     */
    @Insert("""
            INSERT IGNORE INTO cmpt_5159_cp_hourly_winner (
                act_id, cmpt_use_inx, seq, rank_id, phase_id, hour_code, 
                user_uid, anchor_uid, cp_score, award_amount, is_pool_sufficient,
                channel_sid, channel_ssid, create_time, update_time
            ) VALUES (
                #{actId}, #{cmptUseInx}, #{seq}, #{rankId}, #{phaseId}, #{hourCode},
                #{userUid}, #{anchorUid}, #{cpScore}, #{awardAmount}, #{isPoolSufficient},
                #{channelSid}, #{channelSsid}, NOW(), NOW()
            )
            """)
    int insertIgnore(Cmpt5159CpHourlyWinner record);

    /**
     * 根据seq查询记录（幂等性检查）
     */
    @Select("""
            SELECT * FROM cmpt_5159_cp_hourly_winner 
            WHERE act_id = #{actId} AND cmpt_use_inx = #{cmptUseInx} AND seq = #{seq}
            """)
    Cmpt5159CpHourlyWinner selectBySeq(@Param("actId") Long actId, 
                                       @Param("cmptUseInx") Long cmptUseInx, 
                                       @Param("seq") String seq);

    /**
     * 根据日期查询历史冠军
     */
    @Select("""
            SELECT * FROM cmpt_5159_cp_hourly_winner 
            WHERE act_id = #{actId} AND cmpt_use_inx = #{cmptUseInx} 
            AND hour_code LIKE CONCAT(#{dateStr}, '%')
            ORDER BY hour_code DESC
            """)
    List<Cmpt5159CpHourlyWinner> selectByDate(@Param("actId") Long actId, 
                                              @Param("cmptUseInx") Long cmptUseInx, 
                                              @Param("dateStr") String dateStr);

    /**
     * 根据ID查询记录
     */
    @Select("""
            SELECT * FROM cmpt_5159_cp_hourly_winner 
            WHERE id = #{id}
            """)
    Cmpt5159CpHourlyWinner selectById(@Param("id") Long id);

    /**
     * 查询最近的冠军记录
     */
    @Select("""
            SELECT * FROM cmpt_5159_cp_hourly_winner 
            WHERE act_id = #{actId} AND cmpt_use_inx = #{cmptUseInx}
            ORDER BY create_time DESC
            LIMIT #{limit}
            """)
    List<Cmpt5159CpHourlyWinner> selectLatest(@Param("actId") Long actId, 
                                              @Param("cmptUseInx") Long cmptUseInx, 
                                              @Param("limit") Integer limit);
}
