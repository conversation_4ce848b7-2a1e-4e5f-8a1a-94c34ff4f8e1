package com.yy.gameecology.common.db.mapper.gameecology.cmpt;

import com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5159CpHourlyWinner;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * CP小时第一名玩法组件Mapper接口
 * 
 * <AUTHOR> Generated
 * @date 2025-07-23
 */
public interface Cmpt5159CpHourlyWinnerMapper {
    
    /**
     * 插入CP小时第一名记录（幂等性插入）
     * 
     * @param record CP小时第一名记录
     * @return 影响行数
     */
    @Insert("INSERT IGNORE INTO cmpt_5159_cp_hourly_winner " +
            "(act_id, cmpt_use_inx, seq, rank_id, phase_id, hour_code, user_uid, anchor_uid, " +
            "cp_score, award_amount, is_pool_sufficient, channel_sid, channel_ssid, create_time, update_time) " +
            "VALUES (#{actId}, #{cmptUseInx}, #{seq}, #{rankId}, #{phaseId}, #{hourCode}, #{userUid}, #{anchorUid}, " +
            "#{cpScore}, #{awardAmount}, #{isPoolSufficient}, #{channelSid}, #{channelSsid}, NOW(), NOW())")
    int insertIgnore(Cmpt5159CpHourlyWinner record);
    
    /**
     * 检查事件是否已处理（幂等性检查）
     * 
     * @param actId 活动ID
     * @param cmptUseInx 组件使用索引
     * @param seq 事件序列号
     * @return 记录数量
     */
    @Select("SELECT COUNT(*) FROM cmpt_5159_cp_hourly_winner " +
            "WHERE act_id = #{actId} AND cmpt_use_inx = #{cmptUseInx} AND seq = #{seq}")
    int countBySeq(@Param("actId") Long actId, 
                   @Param("cmptUseInx") Long cmptUseInx, 
                   @Param("seq") String seq);
    
    /**
     * 根据日期查询历史小时冠军CP
     * 
     * @param actId 活动ID
     * @param cmptUseInx 组件使用索引
     * @param datePrefix 日期前缀，格式：yyyyMMdd
     * @return CP小时第一名记录列表
     */
    @Select("SELECT * FROM cmpt_5159_cp_hourly_winner " +
            "WHERE act_id = #{actId} AND cmpt_use_inx = #{cmptUseInx} " +
            "AND hour_code LIKE CONCAT(#{datePrefix}, '%') " +
            "ORDER BY hour_code DESC")
    List<Cmpt5159CpHourlyWinner> selectByDate(@Param("actId") Long actId,
                                              @Param("cmptUseInx") Long cmptUseInx,
                                              @Param("datePrefix") String datePrefix);
    
    /**
     * 根据小时编码查询记录
     * 
     * @param actId 活动ID
     * @param cmptUseInx 组件使用索引
     * @param hourCode 小时编码
     * @return CP小时第一名记录
     */
    @Select("SELECT * FROM cmpt_5159_cp_hourly_winner " +
            "WHERE act_id = #{actId} AND cmpt_use_inx = #{cmptUseInx} AND hour_code = #{hourCode}")
    Cmpt5159CpHourlyWinner selectByHourCode(@Param("actId") Long actId,
                                            @Param("cmptUseInx") Long cmptUseInx,
                                            @Param("hourCode") String hourCode);
    
    /**
     * 查询最近的冠军记录
     * 
     * @param actId 活动ID
     * @param cmptUseInx 组件使用索引
     * @param limit 限制条数
     * @return CP小时第一名记录列表
     */
    @Select("SELECT * FROM cmpt_5159_cp_hourly_winner " +
            "WHERE act_id = #{actId} AND cmpt_use_inx = #{cmptUseInx} " +
            "ORDER BY hour_code DESC LIMIT #{limit}")
    List<Cmpt5159CpHourlyWinner> selectLatest(@Param("actId") Long actId,
                                              @Param("cmptUseInx") Long cmptUseInx,
                                              @Param("limit") Integer limit);
    
    /**
     * 根据榜单ID和阶段ID查询记录
     * 
     * @param actId 活动ID
     * @param cmptUseInx 组件使用索引
     * @param rankId 榜单ID
     * @param phaseId 阶段ID
     * @param limit 限制条数
     * @return CP小时第一名记录列表
     */
    @Select("SELECT * FROM cmpt_5159_cp_hourly_winner " +
            "WHERE act_id = #{actId} AND cmpt_use_inx = #{cmptUseInx} " +
            "AND rank_id = #{rankId} AND phase_id = #{phaseId} " +
            "ORDER BY hour_code DESC LIMIT #{limit}")
    List<Cmpt5159CpHourlyWinner> selectByRankAndPhase(@Param("actId") Long actId,
                                                       @Param("cmptUseInx") Long cmptUseInx,
                                                       @Param("rankId") Long rankId,
                                                       @Param("phaseId") Long phaseId,
                                                       @Param("limit") Integer limit);
    
    /**
     * 根据ID查询记录
     * 
     * @param id 记录ID
     * @return CP小时第一名记录
     */
    @Select("SELECT * FROM cmpt_5159_cp_hourly_winner WHERE id = #{id}")
    Cmpt5159CpHourlyWinner selectById(@Param("id") Long id);
}
