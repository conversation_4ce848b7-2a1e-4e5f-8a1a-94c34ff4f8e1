package com.yy.gameecology.common.db.model.gameecology.cmpt;

import com.yy.gameecology.common.annotation.TableColumn;
import lombok.Data;

import java.util.Date;

/**
 * CP小时第一名玩法组件数据模型
 * 对应表：cmpt_5159_cp_hourly_winner
 * 
 * <AUTHOR> Generated
 * @date 2025-07-23
 */
@Data
@TableColumn(underline = true)
public class Cmpt5159CpHourlyWinner {
    
    public static String TABLE_NAME = "cmpt_5159_cp_hourly_winner";
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 活动ID
     */
    private Long actId;
    
    /**
     * 组件使用索引
     */
    private Long cmptUseInx;
    
    /**
     * 事件序列号，用于幂等性控制
     */
    private String seq;
    
    /**
     * 榜单ID
     */
    private Long rankId;
    
    /**
     * 阶段ID
     */
    private Long phaseId;
    
    /**
     * 小时编码，格式：yyyyMMddHH
     */
    private String hourCode;
    
    /**
     * 用户UID
     */
    private Long userUid;
    
    /**
     * 主播UID
     */
    private Long anchorUid;
    
    /**
     * CP分数
     */
    private Long cpScore;
    
    /**
     * 奖励金额，单位：厘
     */
    private Long awardAmount;
    
    /**
     * 奖池是否充足：1-充足，0-不足
     */
    private Integer isPoolSufficient;
    
    /**
     * 房间SID
     */
    private Long channelSid;
    
    /**
     * 房间SSID
     */
    private Long channelSsid;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
}
