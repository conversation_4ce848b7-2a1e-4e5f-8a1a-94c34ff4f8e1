package com.yy.gameecology.common.db.model.gameecology.cmpt;

import java.util.Date;

/**
 * CP小时第一名玩法组件数据表实体类
 * 
 * <AUTHOR> Generated
 * @date 2025-07-23
 */
public class Cmpt5159CpHourlyWinner {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 活动ID
     */
    private Long actId;
    
    /**
     * 组件使用索引
     */
    private Long cmptUseInx;
    
    /**
     * 事件序列号，用于幂等性控制
     */
    private String seq;
    
    /**
     * 榜单ID
     */
    private Long rankId;
    
    /**
     * 阶段ID
     */
    private Long phaseId;
    
    /**
     * 小时编码，格式：yyyyMMddHH
     */
    private String hourCode;
    
    /**
     * 用户UID
     */
    private Long userUid;
    
    /**
     * 主播UID
     */
    private Long anchorUid;
    
    /**
     * CP分数
     */
    private Long cpScore;
    
    /**
     * 奖励金额，单位：厘
     */
    private Long awardAmount;
    
    /**
     * 奖池是否充足：1-充足，0-不足
     */
    private Boolean isPoolSufficient;
    
    /**
     * 房间SID
     */
    private Long channelSid;
    
    /**
     * 房间SSID
     */
    private Long channelSsid;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getActId() {
        return actId;
    }

    public void setActId(Long actId) {
        this.actId = actId;
    }

    public Long getCmptUseInx() {
        return cmptUseInx;
    }

    public void setCmptUseInx(Long cmptUseInx) {
        this.cmptUseInx = cmptUseInx;
    }

    public String getSeq() {
        return seq;
    }

    public void setSeq(String seq) {
        this.seq = seq;
    }

    public Long getRankId() {
        return rankId;
    }

    public void setRankId(Long rankId) {
        this.rankId = rankId;
    }

    public Long getPhaseId() {
        return phaseId;
    }

    public void setPhaseId(Long phaseId) {
        this.phaseId = phaseId;
    }

    public String getHourCode() {
        return hourCode;
    }

    public void setHourCode(String hourCode) {
        this.hourCode = hourCode;
    }

    public Long getUserUid() {
        return userUid;
    }

    public void setUserUid(Long userUid) {
        this.userUid = userUid;
    }

    public Long getAnchorUid() {
        return anchorUid;
    }

    public void setAnchorUid(Long anchorUid) {
        this.anchorUid = anchorUid;
    }

    public Long getCpScore() {
        return cpScore;
    }

    public void setCpScore(Long cpScore) {
        this.cpScore = cpScore;
    }

    public Long getAwardAmount() {
        return awardAmount;
    }

    public void setAwardAmount(Long awardAmount) {
        this.awardAmount = awardAmount;
    }

    public Boolean getIsPoolSufficient() {
        return isPoolSufficient;
    }

    public void setIsPoolSufficient(Boolean isPoolSufficient) {
        this.isPoolSufficient = isPoolSufficient;
    }

    public Long getChannelSid() {
        return channelSid;
    }

    public void setChannelSid(Long channelSid) {
        this.channelSid = channelSid;
    }

    public Long getChannelSsid() {
        return channelSsid;
    }

    public void setChannelSsid(Long channelSsid) {
        this.channelSsid = channelSsid;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "Cmpt5159CpHourlyWinner{" +
                "id=" + id +
                ", actId=" + actId +
                ", cmptUseInx=" + cmptUseInx +
                ", seq='" + seq + '\'' +
                ", rankId=" + rankId +
                ", phaseId=" + phaseId +
                ", hourCode='" + hourCode + '\'' +
                ", userUid=" + userUid +
                ", anchorUid=" + anchorUid +
                ", cpScore=" + cpScore +
                ", awardAmount=" + awardAmount +
                ", isPoolSufficient=" + isPoolSufficient +
                ", channelSid=" + channelSid +
                ", channelSsid=" + channelSsid +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
