<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yy.gameecology.common.db.mapper.cmpt.CpHourlyWinnerComponentMapper">
    
    <resultMap id="BaseResultMap" type="com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5159CpHourlyWinner">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="act_id" jdbcType="BIGINT" property="actId" />
        <result column="cmpt_use_inx" jdbcType="BIGINT" property="cmptUseInx" />
        <result column="seq" jdbcType="VARCHAR" property="seq" />
        <result column="rank_id" jdbcType="BIGINT" property="rankId" />
        <result column="phase_id" jdbcType="BIGINT" property="phaseId" />
        <result column="hour_code" jdbcType="VARCHAR" property="hourCode" />
        <result column="user_uid" jdbcType="BIGINT" property="userUid" />
        <result column="anchor_uid" jdbcType="BIGINT" property="anchorUid" />
        <result column="cp_score" jdbcType="BIGINT" property="cpScore" />
        <result column="user_nick" jdbcType="VARCHAR" property="userNick" />
        <result column="user_avatar" jdbcType="VARCHAR" property="userAvatar" />
        <result column="anchor_nick" jdbcType="VARCHAR" property="anchorNick" />
        <result column="anchor_avatar" jdbcType="VARCHAR" property="anchorAvatar" />
        <result column="user_multi_nick" jdbcType="LONGVARCHAR" property="userMultiNick" />
        <result column="anchor_multi_nick" jdbcType="LONGVARCHAR" property="anchorMultiNick" />
        <result column="award_amount" jdbcType="BIGINT" property="awardAmount" />
        <result column="award_config" jdbcType="LONGVARCHAR" property="awardConfig" />
        <result column="gift_pool_used" jdbcType="BIGINT" property="giftPoolUsed" />
        <result column="is_pool_sufficient" jdbcType="BOOLEAN" property="isPoolSufficient" />
        <result column="channel_sid" jdbcType="BIGINT" property="channelSid" />
        <result column="channel_ssid" jdbcType="BIGINT" property="channelSsid" />
        <result column="watchword_sent" jdbcType="BOOLEAN" property="watchwordSent" />
        <result column="broadcast_sent" jdbcType="BOOLEAN" property="broadcastSent" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>
    
    <sql id="Base_Column_List">
        id, act_id, cmpt_use_inx, seq, rank_id, phase_id, hour_code, user_uid, anchor_uid, 
        cp_score, user_nick, user_avatar, anchor_nick, anchor_avatar, user_multi_nick, 
        anchor_multi_nick, award_amount, award_config, gift_pool_used, is_pool_sufficient, 
        channel_sid, channel_ssid, watchword_sent, broadcast_sent, create_time, update_time
    </sql>
    
    <insert id="insert" parameterType="com.yy.gameecology.common.db.model.gameecology.cmpt.Cmpt5159CpHourlyWinner">
        INSERT INTO cmpt_5159_cp_hourly_winner (
            act_id, cmpt_use_inx, seq, rank_id, phase_id, hour_code, user_uid, anchor_uid,
            cp_score, user_nick, user_avatar, anchor_nick, anchor_avatar, user_multi_nick,
            anchor_multi_nick, award_amount, award_config, gift_pool_used, is_pool_sufficient,
            channel_sid, channel_ssid, watchword_sent, broadcast_sent
        ) VALUES (
            #{actId}, #{cmptUseInx}, #{seq}, #{rankId}, #{phaseId}, #{hourCode}, #{userUid}, #{anchorUid},
            #{cpScore}, #{userNick}, #{userAvatar}, #{anchorNick}, #{anchorAvatar}, #{userMultiNick},
            #{anchorMultiNick}, #{awardAmount}, #{awardConfig}, #{giftPoolUsed}, #{isPoolSufficient},
            #{channelSid}, #{channelSsid}, #{watchwordSent}, #{broadcastSent}
        )
    </insert>
    
    <select id="countBySeq" resultType="int">
        SELECT COUNT(1) 
        FROM cmpt_5159_cp_hourly_winner 
        WHERE act_id = #{actId} 
          AND cmpt_use_inx = #{cmptUseInx} 
          AND seq = #{seq}
    </select>
    
    <select id="selectByHourCodePrefix" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM cmpt_5159_cp_hourly_winner
        WHERE act_id = #{actId}
          AND cmpt_use_inx = #{cmptUseInx}
          AND hour_code LIKE CONCAT(#{hourCodePrefix}, '%')
        ORDER BY hour_code DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>
    
    <select id="selectByHourCodeRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM cmpt_5159_cp_hourly_winner
        WHERE act_id = #{actId}
          AND cmpt_use_inx = #{cmptUseInx}
          AND hour_code >= #{startHourCode}
          AND hour_code <= #{endHourCode}
        ORDER BY hour_code DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>
    
    <update id="updateWatchwordSent">
        UPDATE cmpt_5159_cp_hourly_winner 
        SET watchword_sent = #{watchwordSent}
        WHERE id = #{id}
    </update>
    
    <update id="updateBroadcastSent">
        UPDATE cmpt_5159_cp_hourly_winner 
        SET broadcast_sent = #{broadcastSent}
        WHERE id = #{id}
    </update>
    
    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM cmpt_5159_cp_hourly_winner
        WHERE id = #{id}
    </select>
    
    <select id="selectLatest" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM cmpt_5159_cp_hourly_winner
        WHERE act_id = #{actId}
          AND cmpt_use_inx = #{cmptUseInx}
        ORDER BY create_time DESC
        <if test="limit != null and limit > 0">
            LIMIT #{limit}
        </if>
    </select>
    
</mapper>
